"""
Unit tests for neural network models.

Tests attention modules, hierarchical policies, value networks, and encoders.
"""

import pytest
import torch
import torch.nn as nn
import numpy as np
from unittest.mock import Mock, patch

from src.models.attention_module import AttentionCommModule, PositionalEncoding, SelfAttentionBlock
from src.models.hierarchical_policy import HierarchicalPolicy, HighLevelPolicy, LowLevelPolicy
from src.models.value_network import ValueNetwork, HierarchicalValueNetwork, DualValueNetwork


class TestAttentionCommModule:
    """Test cases for AttentionCommModule."""
    
    @pytest.fixture
    def attention_module(self):
        """Create test attention module."""
        return AttentionCommModule(
            embed_dim=64,
            num_heads=4,
            dropout=0.1,
            max_agents=10
        )
    
    def test_attention_module_initialization(self, attention_module):
        """Test attention module initialization."""
        assert attention_module.embed_dim == 64
        assert attention_module.num_heads == 4
        assert attention_module.head_dim == 16  # 64 / 4
        assert attention_module.max_agents == 10
    
    def test_attention_forward_pass(self, attention_module):
        """Test forward pass of attention module."""
        batch_size = 2
        num_agents = 5
        embed_dim = 64
        
        query_obs = torch.randn(batch_size, embed_dim)
        key_value_obs = torch.randn(batch_size, num_agents, embed_dim)
        
        output, info = attention_module(query_obs, key_value_obs, return_attention=True)
        
        assert output.shape == (batch_size, embed_dim)
        assert isinstance(info, dict)
        assert "attention_weights" in info
        assert "attention_entropy" in info
        assert "communication_efficiency" in info
    
    def test_attention_with_mask(self, attention_module):
        """Test attention with attention mask."""
        batch_size = 2
        num_agents = 5
        embed_dim = 64
        
        query_obs = torch.randn(batch_size, embed_dim)
        key_value_obs = torch.randn(batch_size, num_agents, embed_dim)
        
        # Create attention mask (mask out some agents)
        attention_mask = torch.zeros(batch_size, 1, num_agents)
        attention_mask[:, :, -1] = float('-inf')  # Mask last agent
        
        output, info = attention_module(query_obs, key_value_obs, attention_mask=attention_mask)
        
        assert output.shape == (batch_size, embed_dim)
        assert info["attention_weights"] is None  # Not requested
    
    def test_attention_entropy_computation(self, attention_module):
        """Test attention entropy computation."""
        # Create uniform attention weights
        uniform_weights = torch.ones(2, 1, 5) / 5
        entropy = attention_module._compute_attention_entropy(uniform_weights)
        
        # Uniform distribution should have high entropy
        max_entropy = np.log(5)
        assert entropy > 0.8 * max_entropy
        
        # Create focused attention weights
        focused_weights = torch.zeros(2, 1, 5)
        focused_weights[:, :, 0] = 1.0
        entropy_focused = attention_module._compute_attention_entropy(focused_weights)
        
        # Focused distribution should have low entropy
        assert entropy_focused < 0.1 * max_entropy


class TestPositionalEncoding:
    """Test cases for PositionalEncoding."""
    
    def test_positional_encoding_creation(self):
        """Test positional encoding creation."""
        embed_dim = 32
        max_agents = 8
        
        pos_enc = PositionalEncoding(embed_dim, max_agents)
        
        assert pos_enc.embed_dim == embed_dim
        assert pos_enc.max_agents == max_agents
        assert pos_enc.pe.shape == (1, max_agents, embed_dim)
    
    def test_positional_encoding_forward(self):
        """Test positional encoding forward pass."""
        embed_dim = 32
        max_agents = 8
        batch_size = 3
        num_agents = 5
        
        pos_enc = PositionalEncoding(embed_dim, max_agents)
        
        x = torch.randn(batch_size, num_agents, embed_dim)
        output = pos_enc(x)
        
        assert output.shape == x.shape
        assert not torch.equal(output, x)  # Should be modified


class TestHierarchicalPolicy:
    """Test cases for hierarchical policy networks."""
    
    @pytest.fixture
    def policy_config(self):
        """Policy configuration for testing."""
        return {
            "high_level_policy": {
                "hidden_dims": [64, 32],
                "activation": "relu"
            },
            "low_level_policy": {
                "hidden_dims": [32, 16],
                "activation": "relu"
            }
        }
    
    def test_high_level_policy(self):
        """Test high-level policy."""
        obs_dim = 10
        num_skills = 4
        
        policy = HighLevelPolicy(obs_dim, num_skills)
        
        batch_size = 3
        obs = torch.randn(batch_size, obs_dim)
        
        logits = policy(obs)
        assert logits.shape == (batch_size, num_skills)
        
        # Test skill sampling
        skill, log_prob = policy.sample_skill(obs, deterministic=False)
        assert skill.shape == (batch_size,)
        assert log_prob.shape == (batch_size,)
        
        # Test deterministic sampling
        skill_det, log_prob_det = policy.sample_skill(obs, deterministic=True)
        assert skill_det.shape == (batch_size,)
    
    def test_low_level_policy(self):
        """Test low-level policy."""
        obs_dim = 10
        action_dim = 2
        num_skills = 4
        
        policy = LowLevelPolicy(obs_dim, action_dim, num_skills)
        
        batch_size = 3
        obs = torch.randn(batch_size, obs_dim)
        skill = torch.randint(0, num_skills, (batch_size,))
        
        action_mean, action_log_std = policy(obs, skill)
        assert action_mean.shape == (batch_size, action_dim)
        assert action_log_std.shape == (batch_size, action_dim)
        
        # Test action sampling
        action, log_prob = policy.sample_action(obs, skill, deterministic=False)
        assert action.shape == (batch_size, action_dim)
        assert log_prob.shape == (batch_size, 1)
    
    def test_hierarchical_policy_wrapper(self, policy_config):
        """Test hierarchical policy wrapper."""
        obs_dim = 10
        action_dim = 2
        num_skills = 4
        
        # Test hierarchical mode
        policy = HierarchicalPolicy(obs_dim, action_dim, num_skills, policy_config, hierarchical=True)
        
        assert hasattr(policy, 'high_level_policy')
        assert hasattr(policy, 'low_level_policy')
        
        batch_size = 3
        obs = torch.randn(batch_size, obs_dim)
        skill = torch.randint(0, num_skills, (batch_size,))
        
        action, info = policy.act(obs, skill=skill, deterministic=False)
        assert action.shape == (batch_size, action_dim)
        assert isinstance(info, dict)
        assert "action_log_prob" in info
    
    def test_flat_policy_wrapper(self, policy_config):
        """Test flat (non-hierarchical) policy wrapper."""
        obs_dim = 10
        action_dim = 2
        num_skills = 4
        
        # Test flat mode
        policy = HierarchicalPolicy(obs_dim, action_dim, num_skills, policy_config, hierarchical=False)
        
        assert hasattr(policy, 'flat_policy')
        assert not hasattr(policy, 'high_level_policy')
        
        batch_size = 3
        obs = torch.randn(batch_size, obs_dim)
        
        action, info = policy.act(obs, deterministic=False)
        assert action.shape == (batch_size, action_dim)
        assert isinstance(info, dict)


class TestValueNetwork:
    """Test cases for value networks."""
    
    def test_basic_value_network(self):
        """Test basic value network."""
        obs_dim = 10
        
        value_net = ValueNetwork(obs_dim)
        
        batch_size = 3
        obs = torch.randn(batch_size, obs_dim)
        
        value = value_net(obs)
        assert value.shape == (batch_size, 1)
    
    def test_value_network_with_config(self):
        """Test value network with custom configuration."""
        obs_dim = 10
        config = {
            "hidden_dims": [64, 32, 16],
            "activation": "tanh"
        }
        
        value_net = ValueNetwork(obs_dim, config=config)
        
        batch_size = 3
        obs = torch.randn(batch_size, obs_dim)
        
        value = value_net(obs)
        assert value.shape == (batch_size, 1)
    
    def test_hierarchical_value_network(self):
        """Test hierarchical value network with skill conditioning."""
        obs_dim = 10
        num_skills = 4
        
        value_net = HierarchicalValueNetwork(obs_dim, num_skills)
        
        batch_size = 3
        obs = torch.randn(batch_size, obs_dim)
        skill = torch.randint(0, num_skills, (batch_size,))
        
        value = value_net(obs, skill)
        assert value.shape == (batch_size, 1)
    
    def test_dual_value_network(self):
        """Test dual value network for hierarchical RL."""
        obs_dim = 10
        num_skills = 4
        
        dual_value_net = DualValueNetwork(obs_dim, num_skills)
        
        batch_size = 3
        obs = torch.randn(batch_size, obs_dim)
        skill = torch.randint(0, num_skills, (batch_size,))
        
        # Test high-level value
        high_value = dual_value_net.forward_high(obs)
        assert high_value.shape == (batch_size, 1)
        
        # Test low-level value
        low_value = dual_value_net.forward_low(obs, skill)
        assert low_value.shape == (batch_size, 1)
        
        # Test forward with level specification
        high_value2 = dual_value_net(obs, level="high")
        assert torch.equal(high_value, high_value2)
        
        low_value2 = dual_value_net(obs, skill, level="low")
        assert torch.equal(low_value, low_value2)


class TestModelIntegration:
    """Integration tests for model components."""
    
    def test_attention_policy_integration(self):
        """Test integration between attention and policy modules."""
        embed_dim = 32
        obs_dim = 10
        action_dim = 2
        num_skills = 3
        
        # Create attention module
        attention = AttentionCommModule(embed_dim, num_heads=2)
        
        # Create policy
        policy_config = {"high_level_policy": {"hidden_dims": [32, 16]}}
        policy = HierarchicalPolicy(obs_dim, action_dim, num_skills, policy_config)
        
        batch_size = 2
        num_agents = 4
        
        # Simulate attention communication
        query_obs = torch.randn(batch_size, embed_dim)
        key_value_obs = torch.randn(batch_size, num_agents, embed_dim)
        
        communicated_obs, _ = attention(query_obs, key_value_obs)
        
        # Use communicated observation for policy (would need proper dimension matching in practice)
        # This is a simplified test
        assert communicated_obs.shape == (batch_size, embed_dim)
    
    def test_model_parameter_counting(self):
        """Test parameter counting for models."""
        obs_dim = 10
        action_dim = 2
        num_skills = 4
        
        # Create models
        attention = AttentionCommModule(embed_dim=32, num_heads=2)
        policy = HierarchicalPolicy(obs_dim, action_dim, num_skills, {})
        value_net = ValueNetwork(obs_dim)
        
        # Count parameters
        attention_params = sum(p.numel() for p in attention.parameters())
        policy_params = sum(p.numel() for p in policy.parameters())
        value_params = sum(p.numel() for p in value_net.parameters())
        
        assert attention_params > 0
        assert policy_params > 0
        assert value_params > 0
    
    def test_model_device_movement(self):
        """Test moving models to different devices."""
        attention = AttentionCommModule(embed_dim=32, num_heads=2)
        
        # Move to CPU (should work regardless of current device)
        attention.to(torch.device('cpu'))
        
        # Test that parameters are on correct device
        for param in attention.parameters():
            assert param.device == torch.device('cpu')
    
    def test_model_training_eval_modes(self):
        """Test training and evaluation mode switching."""
        attention = AttentionCommModule(embed_dim=32, num_heads=2)
        
        # Default should be training
        assert attention.training == True
        
        # Switch to eval
        attention.eval()
        assert attention.training == False
        
        # Switch back to train
        attention.train()
        assert attention.training == True


if __name__ == "__main__":
    pytest.main([__file__])
