"""
Scalability experiment script.

This script evaluates how the proposed method scales with increasing
numbers of agents, measuring performance, training time, and resource usage.
"""

import argparse
import logging
import os
import sys
from pathlib import Path
import time
import json
from typing import Dict, Any, List

import numpy as np
import torch
import yaml
from omegaconf import OmegaConf

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from src.main import load_config, setup_device, create_directories
from src.utils.logger import setup_logger
from src.utils.seed_manager import SeedManager
from src.trainers.hierarchical_trainer import HierarchicalTrainer
from src.environments.environment_loader import load_environment
from src.agents.hierarchical_agent import HierarchicalAgent


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Scalability Experiment")
    
    parser.add_argument(
        "--config",
        type=str,
        default="configs/experiments/scalability_sweep.yaml",
        help="Path to experiment configuration"
    )
    parser.add_argument(
        "--agent-counts",
        type=str,
        default="2,3,5,8,10",
        help="Comma-separated list of agent counts to test"
    )
    parser.add_argument(
        "--num-seeds",
        type=int,
        default=5,
        help="Number of random seeds per configuration"
    )
    parser.add_argument(
        "--output-dir",
        type=str,
        default="results/scalability_sweep",
        help="Output directory for results"
    )
    parser.add_argument(
        "--max-timesteps",
        type=int,
        default=500000,
        help="Maximum training timesteps per run"
    )
    parser.add_argument(
        "--parallel",
        action="store_true",
        help="Run experiments in parallel (if supported)"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug mode"
    )
    
    return parser.parse_args()


def run_single_experiment(
    config: Dict[str, Any],
    agent_count: int,
    seed: int,
    output_dir: Path
) -> Dict[str, Any]:
    """
    Run a single scalability experiment.
    
    Args:
        config: Experiment configuration
        agent_count: Number of agents
        seed: Random seed
        output_dir: Output directory
        
    Returns:
        Experiment results
    """
    # Modify config for this experiment
    exp_config = config.copy()
    exp_config["environment"]["num_agents"] = agent_count
    exp_config["experiment"]["seed"] = seed
    exp_config["experiment"]["name"] = f"scalability_{agent_count}agents_seed{seed}"
    
    # Setup logging for this experiment
    exp_output_dir = output_dir / f"agents_{agent_count}" / f"seed_{seed}"
    exp_output_dir.mkdir(parents=True, exist_ok=True)
    
    exp_config["experiment"]["save_dir"] = str(exp_output_dir / "checkpoints")
    exp_config["experiment"]["log_dir"] = str(exp_output_dir / "logs")
    
    logger = setup_logger(
        log_dir=exp_config["experiment"]["log_dir"],
        experiment_name=exp_config["experiment"]["name"]
    )
    
    logger.info(f"Starting scalability experiment: {agent_count} agents, seed {seed}")
    
    try:
        # Setup device and directories
        device = setup_device(exp_config)
        create_directories(exp_config)
        
        # Set random seed
        seed_manager = SeedManager(seed)
        
        # Create environment
        env = load_environment(exp_config["environment"])
        
        # Create agent
        agent = HierarchicalAgent(
            config=exp_config["agent"],
            observation_space=env.observation_space,
            action_space=env.action_space
        )
        
        # Create trainer
        trainer = HierarchicalTrainer(
            agent=agent,
            env=env,
            config=exp_config,
            device=device
        )
        
        # Record start time and memory
        start_time = time.time()
        start_memory = torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
        
        # Train
        trainer.train()
        
        # Record end time and memory
        end_time = time.time()
        end_memory = torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
        
        # Get training statistics
        training_stats = trainer.get_training_stats()
        
        # Evaluate final performance
        eval_results = trainer.evaluate(num_episodes=20)
        
        # Compile results
        results = {
            "agent_count": agent_count,
            "seed": seed,
            "training_time": end_time - start_time,
            "memory_usage": end_memory - start_memory,
            "final_performance": eval_results,
            "training_stats": training_stats,
            "config": exp_config
        }
        
        # Save results
        results_file = exp_output_dir / "results.json"
        with open(results_file, 'w') as f:
            # Convert numpy types for JSON serialization
            json_results = convert_numpy_types(results)
            json.dump(json_results, f, indent=2)
        
        logger.info(f"Experiment completed successfully")
        logger.info(f"Training time: {end_time - start_time:.2f} seconds")
        logger.info(f"Final reward: {eval_results.get('episode_reward', 0):.4f}")
        
        return results
        
    except Exception as e:
        logger.error(f"Experiment failed: {e}")
        if args.debug:
            raise
        return {
            "agent_count": agent_count,
            "seed": seed,
            "error": str(e),
            "success": False
        }
    
    finally:
        # Cleanup
        if 'trainer' in locals():
            trainer.cleanup()


def convert_numpy_types(obj):
    """Convert numpy types to native Python types for JSON serialization."""
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    else:
        return obj


def aggregate_results(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Aggregate results across seeds for each agent count.
    
    Args:
        results: List of experiment results
        
    Returns:
        Aggregated results
    """
    # Group by agent count
    grouped_results = {}
    for result in results:
        if result.get("success", True):  # Only include successful runs
            agent_count = result["agent_count"]
            if agent_count not in grouped_results:
                grouped_results[agent_count] = []
            grouped_results[agent_count].append(result)
    
    # Aggregate statistics
    aggregated = {}
    for agent_count, agent_results in grouped_results.items():
        if not agent_results:
            continue
        
        # Extract metrics
        training_times = [r["training_time"] for r in agent_results]
        memory_usages = [r["memory_usage"] for r in agent_results]
        final_rewards = [r["final_performance"].get("episode_reward", 0) for r in agent_results]
        
        aggregated[agent_count] = {
            "num_runs": len(agent_results),
            "training_time": {
                "mean": np.mean(training_times),
                "std": np.std(training_times),
                "min": np.min(training_times),
                "max": np.max(training_times)
            },
            "memory_usage": {
                "mean": np.mean(memory_usages),
                "std": np.std(memory_usages),
                "min": np.min(memory_usages),
                "max": np.max(memory_usages)
            },
            "final_reward": {
                "mean": np.mean(final_rewards),
                "std": np.std(final_rewards),
                "min": np.min(final_rewards),
                "max": np.max(final_rewards)
            }
        }
    
    return aggregated


def main():
    """Main function."""
    global args
    args = parse_args()
    
    # Parse agent counts
    agent_counts = [int(x.strip()) for x in args.agent_counts.split(",")]
    
    # Load configuration
    with open(args.config, 'r') as f:
        config = yaml.safe_load(f)
    
    # Override with command line arguments
    config["training"]["total_timesteps"] = args.max_timesteps
    
    # Setup output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Setup main logger
    logger = setup_logger(
        log_dir=str(output_dir / "logs"),
        experiment_name="scalability_sweep"
    )
    
    logger.info("Starting scalability sweep experiment")
    logger.info(f"Agent counts: {agent_counts}")
    logger.info(f"Number of seeds: {args.num_seeds}")
    logger.info(f"Output directory: {output_dir}")
    
    # Generate seeds
    seed_manager = SeedManager(42)
    seeds = seed_manager.get_experiment_seeds(args.num_seeds)
    
    # Run experiments
    all_results = []
    total_experiments = len(agent_counts) * len(seeds)
    experiment_count = 0
    
    for agent_count in agent_counts:
        for seed in seeds:
            experiment_count += 1
            logger.info(f"Running experiment {experiment_count}/{total_experiments}: "
                       f"{agent_count} agents, seed {seed}")
            
            result = run_single_experiment(config, agent_count, seed, output_dir)
            all_results.append(result)
    
    # Aggregate results
    logger.info("Aggregating results...")
    aggregated_results = aggregate_results(all_results)
    
    # Save aggregated results
    summary_file = output_dir / "scalability_summary.json"
    with open(summary_file, 'w') as f:
        json.dump(convert_numpy_types(aggregated_results), f, indent=2)
    
    # Save raw results
    raw_results_file = output_dir / "raw_results.json"
    with open(raw_results_file, 'w') as f:
        json.dump(convert_numpy_types(all_results), f, indent=2)
    
    logger.info("Scalability sweep completed!")
    logger.info(f"Results saved to: {output_dir}")
    
    # Print summary
    print("\nScalability Results Summary:")
    print("=" * 50)
    for agent_count in sorted(aggregated_results.keys()):
        stats = aggregated_results[agent_count]
        print(f"Agents: {agent_count:2d} | "
              f"Reward: {stats['final_reward']['mean']:6.3f}±{stats['final_reward']['std']:5.3f} | "
              f"Time: {stats['training_time']['mean']:7.1f}±{stats['training_time']['std']:6.1f}s")


if __name__ == "__main__":
    main()
