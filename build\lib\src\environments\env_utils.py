"""
Environment utilities and helper functions.

This module provides utility functions for working with
multi-agent environments.
"""

import numpy as np
from typing import Dict, <PERSON>, <PERSON><PERSON>, List
from gym import spaces


def normalize_observations(observations: np.ndarray, obs_mean: np.ndarray = None, obs_std: np.ndarray = None) -> np.ndarray:
    """
    Normalize observations using running statistics.
    
    Args:
        observations: Observations to normalize
        obs_mean: Running mean of observations
        obs_std: Running standard deviation of observations
        
    Returns:
        Normalized observations
    """
    if obs_mean is None or obs_std is None:
        return observations
    
    return (observations - obs_mean) / (obs_std + 1e-8)


def get_env_info(env) -> Dict[str, Any]:
    """
    Get comprehensive environment information.
    
    Args:
        env: Environment instance
        
    Returns:
        Dictionary containing environment information
    """
    info = {
        "num_agents": getattr(env, 'num_agents', 1),
        "max_episode_steps": getattr(env, 'max_episode_steps', 100),
        "observation_space": env.observation_space,
        "action_space": env.action_space
    }
    
    # Add space information
    if isinstance(env.observation_space, spaces.Box):
        info["obs_dim"] = env.observation_space.shape[0]
        info["obs_low"] = env.observation_space.low
        info["obs_high"] = env.observation_space.high
    
    if isinstance(env.action_space, spaces.Box):
        info["action_dim"] = env.action_space.shape[0]
        info["action_low"] = env.action_space.low
        info["action_high"] = env.action_space.high
    elif isinstance(env.action_space, spaces.Discrete):
        info["action_dim"] = env.action_space.n
    
    return info


def compute_agent_distances(positions: np.ndarray) -> np.ndarray:
    """
    Compute pairwise distances between agents.
    
    Args:
        positions: Agent positions [num_agents, 2]
        
    Returns:
        Distance matrix [num_agents, num_agents]
    """
    num_agents = positions.shape[0]
    distances = np.zeros((num_agents, num_agents))
    
    for i in range(num_agents):
        for j in range(num_agents):
            if i != j:
                distances[i, j] = np.linalg.norm(positions[i] - positions[j])
    
    return distances


def check_collisions(positions: np.ndarray, agent_radius: float = 0.05) -> List[Tuple[int, int]]:
    """
    Check for collisions between agents.
    
    Args:
        positions: Agent positions [num_agents, 2]
        agent_radius: Radius of each agent
        
    Returns:
        List of collision pairs (agent_i, agent_j)
    """
    collisions = []
    num_agents = positions.shape[0]
    
    for i in range(num_agents):
        for j in range(i + 1, num_agents):
            distance = np.linalg.norm(positions[i] - positions[j])
            if distance < 2 * agent_radius:
                collisions.append((i, j))
    
    return collisions


def compute_coverage_metric(agent_positions: np.ndarray, world_bounds: Tuple[float, float]) -> float:
    """
    Compute coverage metric for agent positions.
    
    Args:
        agent_positions: Agent positions [num_agents, 2]
        world_bounds: World boundaries (min, max)
        
    Returns:
        Coverage score (0 to 1)
    """
    if len(agent_positions) == 0:
        return 0.0
    
    # Create grid for coverage calculation
    grid_size = 20
    world_min, world_max = world_bounds
    x_grid = np.linspace(world_min, world_max, grid_size)
    y_grid = np.linspace(world_min, world_max, grid_size)
    
    covered_cells = 0
    total_cells = grid_size * grid_size
    
    for i in range(grid_size):
        for j in range(grid_size):
            cell_pos = np.array([x_grid[i], y_grid[j]])
            
            # Check if any agent covers this cell
            min_distance = float('inf')
            for agent_pos in agent_positions:
                distance = np.linalg.norm(cell_pos - agent_pos)
                min_distance = min(min_distance, distance)
            
            # Cell is covered if an agent is within coverage radius
            coverage_radius = (world_max - world_min) / (2 * grid_size)
            if min_distance <= coverage_radius:
                covered_cells += 1
    
    return covered_cells / total_cells


class ObservationNormalizer:
    """Online observation normalizer using running statistics."""
    
    def __init__(self, obs_dim: int, clip_range: float = 5.0):
        """
        Initialize observation normalizer.
        
        Args:
            obs_dim: Observation dimension
            clip_range: Range to clip normalized observations
        """
        self.obs_dim = obs_dim
        self.clip_range = clip_range
        
        # Running statistics
        self.count = 0
        self.mean = np.zeros(obs_dim)
        self.var = np.ones(obs_dim)
        self.std = np.ones(obs_dim)
    
    def update(self, observations: np.ndarray) -> None:
        """
        Update running statistics with new observations.
        
        Args:
            observations: New observations [batch_size, obs_dim]
        """
        if observations.ndim == 1:
            observations = observations.reshape(1, -1)
        
        batch_size = observations.shape[0]
        batch_mean = np.mean(observations, axis=0)
        batch_var = np.var(observations, axis=0)
        
        # Update running statistics
        new_count = self.count + batch_size
        
        # Update mean
        delta = batch_mean - self.mean
        new_mean = self.mean + delta * batch_size / new_count
        
        # Update variance
        m_a = self.var * self.count
        m_b = batch_var * batch_size
        M2 = m_a + m_b + delta**2 * self.count * batch_size / new_count
        new_var = M2 / new_count
        
        self.count = new_count
        self.mean = new_mean
        self.var = new_var
        self.std = np.sqrt(new_var + 1e-8)
    
    def normalize(self, observations: np.ndarray) -> np.ndarray:
        """
        Normalize observations.
        
        Args:
            observations: Observations to normalize
            
        Returns:
            Normalized observations
        """
        normalized = (observations - self.mean) / self.std
        return np.clip(normalized, -self.clip_range, self.clip_range)
    
    def denormalize(self, normalized_observations: np.ndarray) -> np.ndarray:
        """
        Denormalize observations.
        
        Args:
            normalized_observations: Normalized observations
            
        Returns:
            Original scale observations
        """
        return normalized_observations * self.std + self.mean
    
    def get_stats(self) -> Dict[str, np.ndarray]:
        """Get current statistics."""
        return {
            "mean": self.mean.copy(),
            "std": self.std.copy(),
            "var": self.var.copy(),
            "count": self.count
        }
    
    def set_stats(self, stats: Dict[str, np.ndarray]) -> None:
        """Set statistics from dictionary."""
        self.mean = stats["mean"].copy()
        self.std = stats["std"].copy()
        self.var = stats["var"].copy()
        self.count = stats["count"]
