"""
Logging setup and management utilities.

This module provides centralized logging configuration and utilities
for tracking training progress and debugging.
"""

import logging
import os
import sys
from pathlib import Path
from typing import Optional
import time
from datetime import datetime


def setup_logger(
    level: str = "INFO",
    log_dir: Optional[str] = None,
    experiment_name: Optional[str] = None,
    console_output: bool = True
) -> logging.Logger:
    """
    Setup centralized logging configuration.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR)
        log_dir: Directory to save log files
        experiment_name: Name of the experiment for log file naming
        console_output: Whether to output to console
        
    Returns:
        Configured logger instance
    """
    # Convert string level to logging constant
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    
    # Create logger
    logger = logging.getLogger("hrl_marl")
    logger.setLevel(numeric_level)
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(numeric_level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # File handler
    if log_dir is not None:
        log_dir = Path(log_dir)
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Create log filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if experiment_name:
            log_filename = f"{experiment_name}_{timestamp}.log"
        else:
            log_filename = f"training_{timestamp}.log"
        
        log_filepath = log_dir / log_filename
        
        file_handler = logging.FileHandler(log_filepath)
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        logger.info(f"Logging to file: {log_filepath}")
    
    return logger


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """
    Get logger instance.
    
    Args:
        name: Logger name (defaults to main logger)
        
    Returns:
        Logger instance
    """
    if name is None:
        name = "hrl_marl"
    return logging.getLogger(name)


class TrainingLogger:
    """Enhanced logger for training progress tracking."""
    
    def __init__(
        self,
        log_dir: str,
        experiment_name: str,
        log_interval: int = 1000
    ):
        """
        Initialize training logger.
        
        Args:
            log_dir: Directory for log files
            experiment_name: Name of the experiment
            log_interval: Interval for logging metrics
        """
        self.log_dir = Path(log_dir)
        self.experiment_name = experiment_name
        self.log_interval = log_interval
        
        # Setup main logger
        self.logger = setup_logger(
            log_dir=str(self.log_dir),
            experiment_name=experiment_name
        )
        
        # Training metrics
        self.step_count = 0
        self.episode_count = 0
        self.start_time = time.time()
        self.last_log_time = time.time()
        
        # Metrics storage
        self.metrics_history = []
        
        # Create metrics file
        self.metrics_file = self.log_dir / f"{experiment_name}_metrics.csv"
        self._init_metrics_file()
    
    def _init_metrics_file(self) -> None:
        """Initialize CSV metrics file."""
        if not self.metrics_file.exists():
            with open(self.metrics_file, 'w') as f:
                f.write("timestamp,step,episode,metric_name,value\n")
    
    def log_step(self, metrics: dict, step: int, episode: int) -> None:
        """
        Log metrics for a training step.
        
        Args:
            metrics: Dictionary of metrics to log
            step: Current training step
            episode: Current episode number
        """
        self.step_count = step
        self.episode_count = episode
        
        # Store metrics
        timestamp = time.time()
        for metric_name, value in metrics.items():
            self.metrics_history.append({
                "timestamp": timestamp,
                "step": step,
                "episode": episode,
                "metric_name": metric_name,
                "value": value
            })
        
        # Log to console at specified interval
        if step % self.log_interval == 0:
            self._log_to_console(metrics, step, episode)
        
        # Write to CSV file
        self._write_metrics_to_csv(metrics, step, episode, timestamp)
    
    def _log_to_console(self, metrics: dict, step: int, episode: int) -> None:
        """Log metrics to console."""
        current_time = time.time()
        elapsed_time = current_time - self.start_time
        steps_per_sec = step / elapsed_time if elapsed_time > 0 else 0
        
        # Format metrics string
        metrics_str = " | ".join([f"{k}: {v:.4f}" for k, v in metrics.items()])
        
        self.logger.info(
            f"Step: {step:8d} | Episode: {episode:6d} | "
            f"Time: {elapsed_time:8.1f}s | SPS: {steps_per_sec:6.1f} | {metrics_str}"
        )
    
    def _write_metrics_to_csv(
        self,
        metrics: dict,
        step: int,
        episode: int,
        timestamp: float
    ) -> None:
        """Write metrics to CSV file."""
        with open(self.metrics_file, 'a') as f:
            for metric_name, value in metrics.items():
                f.write(f"{timestamp},{step},{episode},{metric_name},{value}\n")
    
    def log_episode_end(self, episode_metrics: dict) -> None:
        """
        Log end-of-episode metrics.
        
        Args:
            episode_metrics: Dictionary of episode-level metrics
        """
        self.logger.info(f"Episode {self.episode_count} completed:")
        for metric_name, value in episode_metrics.items():
            self.logger.info(f"  {metric_name}: {value:.4f}")
    
    def log_evaluation(self, eval_metrics: dict) -> None:
        """
        Log evaluation results.
        
        Args:
            eval_metrics: Dictionary of evaluation metrics
        """
        self.logger.info("Evaluation results:")
        for metric_name, value in eval_metrics.items():
            self.logger.info(f"  {metric_name}: {value:.4f}")
    
    def log_checkpoint_save(self, checkpoint_path: str) -> None:
        """Log checkpoint save."""
        self.logger.info(f"Checkpoint saved: {checkpoint_path}")
    
    def log_experiment_start(self, config: dict) -> None:
        """
        Log experiment start with configuration.
        
        Args:
            config: Experiment configuration
        """
        self.logger.info("=" * 80)
        self.logger.info(f"Starting experiment: {self.experiment_name}")
        self.logger.info("=" * 80)
        
        # Log key configuration parameters
        self.logger.info("Configuration:")
        for key, value in config.items():
            if isinstance(value, dict):
                self.logger.info(f"  {key}:")
                for subkey, subvalue in value.items():
                    self.logger.info(f"    {subkey}: {subvalue}")
            else:
                self.logger.info(f"  {key}: {value}")
    
    def log_experiment_end(self) -> None:
        """Log experiment completion."""
        total_time = time.time() - self.start_time
        self.logger.info("=" * 80)
        self.logger.info(f"Experiment completed: {self.experiment_name}")
        self.logger.info(f"Total time: {total_time:.1f} seconds")
        self.logger.info(f"Total steps: {self.step_count}")
        self.logger.info(f"Total episodes: {self.episode_count}")
        self.logger.info("=" * 80)
    
    def get_metrics_history(self) -> list:
        """Get complete metrics history."""
        return self.metrics_history.copy()


class DebugLogger:
    """Debug logger for detailed debugging information."""
    
    def __init__(self, enabled: bool = False):
        """
        Initialize debug logger.
        
        Args:
            enabled: Whether debug logging is enabled
        """
        self.enabled = enabled
        self.logger = get_logger("debug")
        
        if enabled:
            self.logger.setLevel(logging.DEBUG)
    
    def log_tensor_stats(self, tensor_name: str, tensor) -> None:
        """Log tensor statistics."""
        if not self.enabled:
            return
        
        if hasattr(tensor, 'shape'):
            stats = {
                "shape": tuple(tensor.shape),
                "mean": float(tensor.mean()) if tensor.numel() > 0 else 0.0,
                "std": float(tensor.std()) if tensor.numel() > 0 else 0.0,
                "min": float(tensor.min()) if tensor.numel() > 0 else 0.0,
                "max": float(tensor.max()) if tensor.numel() > 0 else 0.0
            }
            self.logger.debug(f"{tensor_name}: {stats}")
    
    def log_gradient_stats(self, model) -> None:
        """Log gradient statistics for model parameters."""
        if not self.enabled:
            return
        
        total_norm = 0.0
        param_count = 0
        
        for name, param in model.named_parameters():
            if param.grad is not None:
                param_norm = param.grad.data.norm(2)
                total_norm += param_norm.item() ** 2
                param_count += 1
                
                self.logger.debug(f"Gradient {name}: norm={param_norm:.6f}")
        
        total_norm = total_norm ** (1. / 2)
        self.logger.debug(f"Total gradient norm: {total_norm:.6f} ({param_count} parameters)")
    
    def log_attention_stats(self, attention_weights) -> None:
        """Log attention weight statistics."""
        if not self.enabled or attention_weights is None:
            return
        
        entropy = -torch.sum(attention_weights * torch.log(attention_weights + 1e-8), dim=-1)
        
        stats = {
            "shape": tuple(attention_weights.shape),
            "mean_entropy": float(entropy.mean()),
            "max_weight": float(attention_weights.max()),
            "min_weight": float(attention_weights.min())
        }
        
        self.logger.debug(f"Attention stats: {stats}")
