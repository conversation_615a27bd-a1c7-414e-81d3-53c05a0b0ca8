"""Setup script for Scalable Decentralized HRL project."""

from setuptools import setup, find_packages
import os

# Read the README file
with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

# Read requirements
with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="hrl-attention-marl",
    version="0.1.0",
    author="Research Team",
    author_email="<EMAIL>",
    description="Scalable Decentralized Hierarchical Reinforcement Learning with Attention-Based Communication",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/username/hrl-attention-marl",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=3.0.0",
            "black>=22.0.0",
            "flake8>=4.0.0",
            "mypy>=0.931",
            "pre-commit>=2.17.0",
        ],
        "gpu": [
            "torch>=1.9.0+cu111",
            "torchvision>=0.10.0+cu111",
        ],
        "distributed": [
            "ray[rllib]>=1.13.0",
            "mpi4py>=3.1.0",
        ],
        "analysis": [
            "jupyter>=1.0.0",
            "ipython>=8.0.0",
            "plotly>=5.6.0",
            "networkx>=2.7.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "hrl-train=src.main:main",
            "hrl-eval=src.evaluate:main",
            "hrl-analyze=analysis.plot_results:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.yaml", "*.yml", "*.json", "*.txt"],
    },
    keywords=[
        "reinforcement learning",
        "multi-agent",
        "hierarchical rl",
        "attention mechanism",
        "decentralized coordination",
        "swarm robotics",
        "deep learning",
        "pytorch",
    ],
    project_urls={
        "Bug Reports": "https://github.com/username/hrl-attention-marl/issues",
        "Source": "https://github.com/username/hrl-attention-marl",
        "Documentation": "https://hrl-attention-marl.readthedocs.io/",
    },
)
