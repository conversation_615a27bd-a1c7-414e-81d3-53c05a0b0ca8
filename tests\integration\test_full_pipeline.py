"""
Integration tests for the complete pipeline.

Tests end-to-end workflows including configuration loading,
experiment execution, and result analysis.
"""

import pytest
import torch
import numpy as np
import tempfile
import shutil
import yaml
import json
from pathlib import Path
from unittest.mock import Mock, patch

from src.main import load_config, setup_device, create_directories
from src.agents.hierarchical_agent import HierarchicalAgent
from src.environments.environment_loader import load_environment
from src.trainers.hierarchical_trainer import HierarchicalTrainer
from src.utils.seed_manager import set_global_seed


class TestFullPipeline:
    """Integration tests for the complete pipeline."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test outputs."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def test_config_file(self, temp_dir):
        """Create a test configuration file."""
        config = {
            "experiment": {
                "name": "pipeline_test",
                "seed": 42,
                "save_dir": str(Path(temp_dir) / "checkpoints"),
                "log_dir": str(Path(temp_dir) / "logs"),
                "device": "auto"
            },
            "environment": {
                "name": "moving_goals",
                "num_agents": 2,
                "max_episode_steps": 8
            },
            "agent": {
                "hierarchical": True,
                "num_skills": 2,
                "skill_length": 3,
                "attention": {
                    "enabled": True,
                    "embed_dim": 16,
                    "num_heads": 2,
                    "dropout": 0.1,
                    "max_agents": 5
                },
                "high_level_policy": {
                    "hidden_dims": [32, 16],
                    "activation": "relu"
                },
                "low_level_policy": {
                    "hidden_dims": [16, 8],
                    "activation": "relu"
                },
                "value_network": {
                    "hidden_dims": [32, 16],
                    "activation": "relu"
                }
            },
            "training": {
                "algorithm": "ppo",
                "total_timesteps": 30,
                "learning_rate": 1e-3,
                "batch_size": 3,
                "num_epochs": 2,
                "eval_freq": 15,
                "eval_episodes": 2
            },
            "logging": {
                "level": "INFO",
                "log_interval": 10,
                "tensorboard": False,
                "wandb": False,
                "metrics": ["episode_reward", "episode_length", "coordination_score"]
            },
            "checkpoint": {
                "save_best": True,
                "save_last": True,
                "save_interval": 20,
                "max_checkpoints": 3
            }
        }
        
        config_file = Path(temp_dir) / "test_config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(config, f)
        
        return str(config_file)
    
    def test_config_loading_and_parsing(self, test_config_file):
        """Test configuration loading and parsing."""
        # Mock command line arguments
        class MockArgs:
            def __init__(self):
                self.seed = None
                self.device = None
                self.num_agents = None
                self.env_name = None
                self.total_timesteps = None
                self.learning_rate = None
                self.batch_size = None
                self.eval_episodes = None
                self.wandb = False
                self.tensorboard = False
                self.debug = False
                self.profile = False
        
        args = MockArgs()
        config = load_config(test_config_file, args)
        
        # Check that config was loaded correctly
        assert isinstance(config, dict)
        assert "experiment" in config
        assert "environment" in config
        assert "agent" in config
        assert "training" in config
        
        # Check specific values
        assert config["experiment"]["name"] == "pipeline_test"
        assert config["environment"]["num_agents"] == 2
        assert config["agent"]["hierarchical"] == True
        assert config["training"]["algorithm"] == "ppo"
    
    def test_config_override_with_args(self, test_config_file):
        """Test configuration override with command line arguments."""
        class MockArgs:
            def __init__(self):
                self.seed = 123
                self.device = "cpu"
                self.num_agents = 5
                self.env_name = "custom_swarm"
                self.total_timesteps = 1000
                self.learning_rate = 1e-4
                self.batch_size = 64
                self.eval_episodes = 10
                self.wandb = True
                self.tensorboard = True
                self.debug = True
                self.profile = True
        
        args = MockArgs()
        config = load_config(test_config_file, args)
        
        # Check that overrides were applied
        assert config["experiment"]["seed"] == 123
        assert config["experiment"]["device"] == "cpu"
        assert config["environment"]["num_agents"] == 5
        assert config["environment"]["name"] == "custom_swarm"
        assert config["training"]["total_timesteps"] == 1000
        assert config["training"]["learning_rate"] == 1e-4
        assert config["training"]["batch_size"] == 64
        assert config["training"]["eval_episodes"] == 10
        assert config["logging"]["wandb"] == True
        assert config["logging"]["tensorboard"] == True
        assert config["debug"]["enabled"] == True
        assert config["debug"]["profile"] == True
    
    def test_device_setup(self, test_config_file):
        """Test device setup functionality."""
        class MockArgs:
            def __init__(self):
                self.device = None
                # ... other attributes set to None/False
                for attr in ['seed', 'num_agents', 'env_name', 'total_timesteps', 
                           'learning_rate', 'batch_size', 'eval_episodes', 'wandb', 
                           'tensorboard', 'debug', 'profile']:
                    setattr(self, attr, None if attr not in ['wandb', 'tensorboard', 'debug', 'profile'] else False)
        
        args = MockArgs()
        config = load_config(test_config_file, args)
        
        # Test auto device selection
        device = setup_device(config)
        assert isinstance(device, torch.device)
        
        # Test explicit CPU device
        config["experiment"]["device"] = "cpu"
        device = setup_device(config)
        assert device == torch.device("cpu")
    
    def test_directory_creation(self, test_config_file, temp_dir):
        """Test directory creation functionality."""
        class MockArgs:
            def __init__(self):
                for attr in ['seed', 'device', 'num_agents', 'env_name', 'total_timesteps', 
                           'learning_rate', 'batch_size', 'eval_episodes', 'wandb', 
                           'tensorboard', 'debug', 'profile']:
                    setattr(self, attr, None if attr not in ['wandb', 'tensorboard', 'debug', 'profile'] else False)
        
        args = MockArgs()
        config = load_config(test_config_file, args)
        
        # Create directories
        create_directories(config)
        
        # Check that directories were created
        save_dir = Path(config["experiment"]["save_dir"])
        log_dir = Path(config["experiment"]["log_dir"])
        
        assert save_dir.exists()
        assert log_dir.exists()
        
        # Check experiment-specific subdirectories
        exp_name = config["experiment"]["name"]
        assert (save_dir / exp_name).exists()
        assert (log_dir / exp_name).exists()
    
    def test_end_to_end_training_pipeline(self, test_config_file):
        """Test complete end-to-end training pipeline."""
        class MockArgs:
            def __init__(self):
                for attr in ['seed', 'device', 'num_agents', 'env_name', 'total_timesteps', 
                           'learning_rate', 'batch_size', 'eval_episodes', 'wandb', 
                           'tensorboard', 'debug', 'profile']:
                    setattr(self, attr, None if attr not in ['wandb', 'tensorboard', 'debug', 'profile'] else False)
        
        args = MockArgs()
        config = load_config(test_config_file, args)
        
        # Setup components
        set_global_seed(config["experiment"]["seed"])
        device = setup_device(config)
        create_directories(config)
        
        # Create environment
        env = load_environment(config["environment"])
        
        # Create agent
        agent = HierarchicalAgent(
            config=config["agent"],
            observation_space=env.observation_space,
            action_space=env.action_space
        )
        
        # Create trainer
        trainer = HierarchicalTrainer(
            agent=agent,
            env=env,
            config=config,
            device=device
        )
        
        # Mock the RL algorithm to avoid complex training
        with patch.object(trainer.rl_algorithm, 'update') as mock_update:
            mock_update.return_value = {
                "policy_loss": 0.5,
                "value_loss": 0.3,
                "entropy_loss": 0.1,
                "total_loss": 0.9
            }
            
            # Run training
            try:
                trainer.train()
                pipeline_success = True
            except Exception as e:
                pipeline_success = False
                print(f"Pipeline failed: {e}")
        
        assert pipeline_success
        
        # Check that training progressed
        assert trainer.step_count > 0
        assert trainer.episode_count > 0
        
        # Check that files were created
        save_dir = Path(config["experiment"]["save_dir"]) / config["experiment"]["name"]
        log_dir = Path(config["experiment"]["log_dir"]) / config["experiment"]["name"]
        
        # Should have some log files
        assert log_dir.exists()
        
        # Cleanup
        trainer.cleanup()
    
    def test_evaluation_pipeline(self, test_config_file):
        """Test evaluation pipeline."""
        class MockArgs:
            def __init__(self):
                for attr in ['seed', 'device', 'num_agents', 'env_name', 'total_timesteps', 
                           'learning_rate', 'batch_size', 'eval_episodes', 'wandb', 
                           'tensorboard', 'debug', 'profile']:
                    setattr(self, attr, None if attr not in ['wandb', 'tensorboard', 'debug', 'profile'] else False)
        
        args = MockArgs()
        config = load_config(test_config_file, args)
        
        # Setup components
        set_global_seed(config["experiment"]["seed"])
        device = setup_device(config)
        
        env = load_environment(config["environment"])
        agent = HierarchicalAgent(
            config=config["agent"],
            observation_space=env.observation_space,
            action_space=env.action_space
        )
        trainer = HierarchicalTrainer(
            agent=agent,
            env=env,
            config=config,
            device=device
        )
        
        # Run evaluation
        eval_results = trainer.evaluate(num_episodes=2)
        
        assert isinstance(eval_results, dict)
        assert "episode_reward" in eval_results
        assert "episode_length" in eval_results
        
        # Check that results are reasonable
        assert isinstance(eval_results["episode_reward"], (int, float))
        assert eval_results["episode_length"] > 0
        
        trainer.cleanup()
    
    def test_checkpoint_pipeline(self, test_config_file, temp_dir):
        """Test checkpoint saving and loading pipeline."""
        class MockArgs:
            def __init__(self):
                for attr in ['seed', 'device', 'num_agents', 'env_name', 'total_timesteps', 
                           'learning_rate', 'batch_size', 'eval_episodes', 'wandb', 
                           'tensorboard', 'debug', 'profile']:
                    setattr(self, attr, None if attr not in ['wandb', 'tensorboard', 'debug', 'profile'] else False)
        
        args = MockArgs()
        config = load_config(test_config_file, args)
        
        # Setup components
        set_global_seed(config["experiment"]["seed"])
        device = setup_device(config)
        create_directories(config)
        
        env = load_environment(config["environment"])
        agent = HierarchicalAgent(
            config=config["agent"],
            observation_space=env.observation_space,
            action_space=env.action_space
        )
        trainer = HierarchicalTrainer(
            agent=agent,
            env=env,
            config=config,
            device=device
        )
        
        # Save checkpoint
        checkpoint_path = trainer.save_checkpoint("test_pipeline")
        assert Path(checkpoint_path).exists()
        
        # Modify trainer state
        original_step_count = trainer.step_count
        trainer.step_count = 999
        
        # Load checkpoint
        trainer.load_checkpoint(checkpoint_path)
        
        # Verify loading worked (state should be restored or at least not crash)
        assert isinstance(trainer.step_count, int)
        
        trainer.cleanup()
    
    def test_multi_environment_compatibility(self, temp_dir):
        """Test pipeline compatibility with different environments."""
        environments_to_test = ["moving_goals", "custom_swarm"]
        
        for env_name in environments_to_test:
            config = {
                "experiment": {
                    "name": f"test_{env_name}",
                    "seed": 42,
                    "save_dir": str(Path(temp_dir) / "checkpoints"),
                    "log_dir": str(Path(temp_dir) / "logs"),
                    "device": "cpu"
                },
                "environment": {
                    "name": env_name,
                    "num_agents": 2,
                    "max_episode_steps": 5
                },
                "agent": {
                    "hierarchical": True,
                    "num_skills": 2,
                    "skill_length": 2,
                    "attention": {
                        "enabled": True,
                        "embed_dim": 8,
                        "num_heads": 1
                    }
                },
                "training": {
                    "algorithm": "ppo",
                    "total_timesteps": 10,
                    "batch_size": 2
                },
                "logging": {"metrics": ["episode_reward"]},
                "checkpoint": {"save_best": False, "save_last": False}
            }
            
            # Test that environment can be created and used
            env = load_environment(config["environment"])
            agent = HierarchicalAgent(
                config=config["agent"],
                observation_space=env.observation_space,
                action_space=env.action_space
            )
            
            # Test basic interaction
            obs_list = env.reset()
            assert len(obs_list) == config["environment"]["num_agents"]
            
            actions = []
            for obs in obs_list:
                action, _ = agent.act(obs, other_observations=obs_list)
                actions.append(action)
            
            next_obs_list, rewards, dones, info = env.step(actions)
            
            assert len(next_obs_list) == len(obs_list)
            assert len(rewards) == len(obs_list)
            assert len(dones) == len(obs_list)
    
    def test_configuration_validation(self, temp_dir):
        """Test configuration validation and error handling."""
        # Test invalid environment name
        invalid_config = {
            "experiment": {"name": "test", "seed": 42, "save_dir": temp_dir, "log_dir": temp_dir},
            "environment": {"name": "invalid_env", "num_agents": 2},
            "agent": {"hierarchical": True},
            "training": {"algorithm": "ppo"},
            "logging": {"metrics": []},
            "checkpoint": {}
        }
        
        with pytest.raises(ValueError, match="Unknown environment"):
            load_environment(invalid_config["environment"])
    
    def test_reproducibility_across_pipeline_runs(self, test_config_file):
        """Test that complete pipeline runs are reproducible."""
        results = []
        
        for run in range(2):
            class MockArgs:
                def __init__(self):
                    for attr in ['seed', 'device', 'num_agents', 'env_name', 'total_timesteps', 
                               'learning_rate', 'batch_size', 'eval_episodes', 'wandb', 
                               'tensorboard', 'debug', 'profile']:
                        setattr(self, attr, None if attr not in ['wandb', 'tensorboard', 'debug', 'profile'] else False)
            
            args = MockArgs()
            config = load_config(test_config_file, args)
            
            # Ensure same seed
            set_global_seed(42)
            
            device = setup_device(config)
            env = load_environment(config["environment"])
            agent = HierarchicalAgent(
                config=config["agent"],
                observation_space=env.observation_space,
                action_space=env.action_space
            )
            
            # Collect a few episodes deterministically
            episode_rewards = []
            for _ in range(2):
                obs_list = env.reset()
                episode_reward = 0
                
                for step in range(3):
                    actions = []
                    for i, obs in enumerate(obs_list):
                        other_obs = [obs_list[j] for j in range(len(obs_list)) if j != i]
                        action, _ = agent.act(obs, other_observations=other_obs, deterministic=True)
                        actions.append(action)
                    
                    obs_list, rewards, dones, _ = env.step(actions)
                    episode_reward += sum(rewards)
                    
                    if any(dones):
                        break
                
                episode_rewards.append(episode_reward)
            
            results.append(episode_rewards)
        
        # Results should be very similar (allowing for small floating point differences)
        for i in range(len(results[0])):
            assert abs(results[0][i] - results[1][i]) < 1e-5


if __name__ == "__main__":
    pytest.main([__file__])
