"""
Multi-head attention module for inter-agent communication.

This module implements the attention mechanism that enables agents
to selectively communicate and coordinate with each other.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional, Dict, Any
import math


class AttentionCommModule(nn.Module):
    """Multi-head attention module for agent communication."""
    
    def __init__(
        self,
        embed_dim: int,
        num_heads: int = 4,
        dropout: float = 0.1,
        max_agents: int = 20,
        use_positional_encoding: bool = True
    ):
        """
        Initialize attention communication module.
        
        Args:
            embed_dim: Embedding dimension
            num_heads: Number of attention heads
            dropout: Dropout probability
            max_agents: Maximum number of agents
            use_positional_encoding: Whether to use positional encoding
        """
        super().__init__()
        
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.dropout = dropout
        self.max_agents = max_agents
        self.use_positional_encoding = use_positional_encoding
        
        assert embed_dim % num_heads == 0, "embed_dim must be divisible by num_heads"
        self.head_dim = embed_dim // num_heads
        
        # Multi-head attention
        self.multihead_attn = nn.MultiheadAttention(
            embed_dim=embed_dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )
        
        # Input projection layers
        self.input_projection = nn.Linear(embed_dim, embed_dim)
        
        # Output projection
        self.output_projection = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim)
        )
        
        # Layer normalization
        self.layer_norm1 = nn.LayerNorm(embed_dim)
        self.layer_norm2 = nn.LayerNorm(embed_dim)
        
        # Positional encoding
        if use_positional_encoding:
            self.positional_encoding = PositionalEncoding(embed_dim, max_agents)
        
        # Communication mask for selective attention
        self.register_buffer("communication_mask", torch.zeros(max_agents, max_agents))
        
    def forward(
        self,
        query_obs: torch.Tensor,
        key_value_obs: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        return_attention: bool = False
    ) -> Tuple[torch.Tensor, Dict[str, Any]]:
        """
        Forward pass of attention communication.
        
        Args:
            query_obs: Query observations [batch_size, embed_dim]
            key_value_obs: Key-value observations [batch_size, num_agents, embed_dim]
            attention_mask: Optional attention mask
            return_attention: Whether to return attention weights
            
        Returns:
            Tuple of (communicated_obs, info_dict)
        """
        batch_size = query_obs.size(0)
        num_agents = key_value_obs.size(1)
        
        # Project inputs
        query = self.input_projection(query_obs).unsqueeze(1)  # [batch, 1, embed_dim]
        key_value = self.input_projection(key_value_obs)  # [batch, num_agents, embed_dim]
        
        # Add positional encoding
        if self.use_positional_encoding:
            key_value = self.positional_encoding(key_value)
        
        # Apply attention
        attended_output, attention_weights = self.multihead_attn(
            query=query,
            key=key_value,
            value=key_value,
            attn_mask=attention_mask,
            need_weights=True
        )
        
        # Residual connection and layer norm
        attended_output = self.layer_norm1(query + attended_output)
        
        # Feed-forward network
        output = self.output_projection(attended_output)
        output = self.layer_norm2(attended_output + output)
        
        # Remove sequence dimension
        communicated_obs = output.squeeze(1)  # [batch, embed_dim]
        
        # Prepare info dictionary
        info = {
            "attention_weights": attention_weights.detach() if return_attention else None,
            "attention_entropy": self._compute_attention_entropy(attention_weights),
            "communication_efficiency": self._compute_communication_efficiency(attention_weights)
        }
        
        return communicated_obs, info
    
    def _compute_attention_entropy(self, attention_weights: torch.Tensor) -> float:
        """Compute entropy of attention weights."""
        # attention_weights: [batch, 1, num_agents]
        probs = attention_weights.squeeze(1)  # [batch, num_agents]
        entropy = -torch.sum(probs * torch.log(probs + 1e-8), dim=-1)
        return entropy.mean().item()
    
    def _compute_communication_efficiency(self, attention_weights: torch.Tensor) -> float:
        """Compute communication efficiency metric."""
        # Measure how focused the attention is (inverse of entropy)
        entropy = self._compute_attention_entropy(attention_weights)
        max_entropy = math.log(attention_weights.size(-1))
        efficiency = 1.0 - (entropy / max_entropy)
        return efficiency
    
    def set_communication_mask(self, mask: torch.Tensor) -> None:
        """
        Set communication mask for selective attention.
        
        Args:
            mask: Boolean mask [max_agents, max_agents]
        """
        self.communication_mask = mask.to(self.communication_mask.device)
    
    def reset(self) -> None:
        """Reset module state."""
        # Reset any internal state if needed
        pass


class PositionalEncoding(nn.Module):
    """Positional encoding for agent positions in attention."""
    
    def __init__(self, embed_dim: int, max_agents: int):
        """
        Initialize positional encoding.
        
        Args:
            embed_dim: Embedding dimension
            max_agents: Maximum number of agents
        """
        super().__init__()
        
        self.embed_dim = embed_dim
        self.max_agents = max_agents
        
        # Create positional encoding matrix
        pe = torch.zeros(max_agents, embed_dim)
        position = torch.arange(0, max_agents, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(
            torch.arange(0, embed_dim, 2).float() * 
            (-math.log(10000.0) / embed_dim)
        )
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        self.register_buffer('pe', pe.unsqueeze(0))  # [1, max_agents, embed_dim]
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Add positional encoding to input.
        
        Args:
            x: Input tensor [batch_size, num_agents, embed_dim]
            
        Returns:
            Tensor with positional encoding added
        """
        batch_size, num_agents, embed_dim = x.size()
        
        # Add positional encoding
        x = x + self.pe[:, :num_agents, :]
        
        return x


class SelfAttentionBlock(nn.Module):
    """Self-attention block for processing agent observations."""
    
    def __init__(
        self,
        embed_dim: int,
        num_heads: int = 4,
        dropout: float = 0.1,
        feedforward_dim: int = None
    ):
        """
        Initialize self-attention block.
        
        Args:
            embed_dim: Embedding dimension
            num_heads: Number of attention heads
            dropout: Dropout probability
            feedforward_dim: Feedforward network dimension
        """
        super().__init__()
        
        if feedforward_dim is None:
            feedforward_dim = 4 * embed_dim
        
        # Self-attention
        self.self_attn = nn.MultiheadAttention(
            embed_dim=embed_dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )
        
        # Feedforward network
        self.feedforward = nn.Sequential(
            nn.Linear(embed_dim, feedforward_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(feedforward_dim, embed_dim)
        )
        
        # Layer normalization
        self.norm1 = nn.LayerNorm(embed_dim)
        self.norm2 = nn.LayerNorm(embed_dim)
        
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of self-attention block.
        
        Args:
            x: Input tensor [batch_size, seq_len, embed_dim]
            
        Returns:
            Output tensor with same shape
        """
        # Self-attention with residual connection
        attn_output, _ = self.self_attn(x, x, x)
        x = self.norm1(x + self.dropout(attn_output))
        
        # Feedforward with residual connection
        ff_output = self.feedforward(x)
        x = self.norm2(x + self.dropout(ff_output))
        
        return x
