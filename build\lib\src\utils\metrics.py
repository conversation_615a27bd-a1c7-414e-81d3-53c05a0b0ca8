"""
Metrics computation and tracking utilities.

This module provides utilities for computing and tracking
various metrics used in multi-agent reinforcement learning.
"""

import numpy as np
import torch
from typing import Dict, Any, List, Optional
from collections import defaultdict, deque
import math


class MetricsTracker:
    """Tracker for training and evaluation metrics."""
    
    def __init__(self, metrics_list: List[str], window_size: int = 100):
        """
        Initialize metrics tracker.
        
        Args:
            metrics_list: List of metric names to track
            window_size: Window size for moving averages
        """
        self.metrics_list = metrics_list
        self.window_size = window_size
        
        # Storage for metrics
        self.metrics_history = defaultdict(list)
        self.metrics_windows = defaultdict(lambda: deque(maxlen=window_size))
        
        # Summary statistics
        self.summary_stats = defaultdict(dict)
    
    def update(self, metrics: Dict[str, float]) -> None:
        """
        Update metrics with new values.
        
        Args:
            metrics: Dictionary of metric values
        """
        for metric_name, value in metrics.items():
            if metric_name in self.metrics_list:
                self.metrics_history[metric_name].append(value)
                self.metrics_windows[metric_name].append(value)
                
                # Update summary statistics
                self._update_summary_stats(metric_name)
    
    def _update_summary_stats(self, metric_name: str) -> None:
        """Update summary statistics for a metric."""
        values = list(self.metrics_windows[metric_name])
        
        if values:
            self.summary_stats[metric_name] = {
                "mean": np.mean(values),
                "std": np.std(values),
                "min": np.min(values),
                "max": np.max(values),
                "count": len(values)
            }
    
    def get_current_stats(self) -> Dict[str, Dict[str, float]]:
        """Get current summary statistics."""
        return dict(self.summary_stats)
    
    def get_metric_history(self, metric_name: str) -> List[float]:
        """Get full history for a specific metric."""
        return self.metrics_history[metric_name].copy()
    
    def get_history(self) -> Dict[str, List[float]]:
        """Get full history for all metrics."""
        return {name: values.copy() for name, values in self.metrics_history.items()}
    
    def reset(self) -> None:
        """Reset all metrics."""
        self.metrics_history.clear()
        self.metrics_windows.clear()
        self.summary_stats.clear()


def compute_coordination_score(
    agent_positions: np.ndarray,
    target_positions: np.ndarray,
    agent_actions: Optional[np.ndarray] = None
) -> float:
    """
    Compute coordination score based on agent positions and targets.
    
    Args:
        agent_positions: Agent positions [num_agents, 2]
        target_positions: Target positions [num_targets, 2]
        agent_actions: Optional agent actions for coordination analysis
        
    Returns:
        Coordination score (higher is better)
    """
    num_agents = agent_positions.shape[0]
    num_targets = target_positions.shape[0]
    
    if num_agents == 0 or num_targets == 0:
        return 0.0
    
    # Compute distances from agents to targets
    distances = np.linalg.norm(
        agent_positions[:, np.newaxis, :] - target_positions[np.newaxis, :, :],
        axis=2
    )
    
    # Find minimum distance assignment (Hungarian algorithm approximation)
    min_distances = []
    used_targets = set()
    
    for _ in range(min(num_agents, num_targets)):
        # Find minimum distance
        min_dist = np.inf
        best_agent, best_target = -1, -1
        
        for i in range(num_agents):
            for j in range(num_targets):
                if j not in used_targets and distances[i, j] < min_dist:
                    min_dist = distances[i, j]
                    best_agent, best_target = i, j
        
        if best_agent != -1:
            min_distances.append(min_dist)
            used_targets.add(best_target)
    
    # Compute coordination score (inverse of average distance)
    if min_distances:
        avg_distance = np.mean(min_distances)
        coordination_score = 1.0 / (1.0 + avg_distance)
    else:
        coordination_score = 0.0
    
    return coordination_score


def compute_skill_diversity(skill_usage: np.ndarray) -> float:
    """
    Compute diversity of skill usage.
    
    Args:
        skill_usage: Array of skill usage counts [num_skills]
        
    Returns:
        Skill diversity score (entropy-based)
    """
    if skill_usage.sum() == 0:
        return 0.0
    
    # Normalize to probabilities
    skill_probs = skill_usage / skill_usage.sum()
    
    # Compute entropy
    entropy = -np.sum(skill_probs * np.log(skill_probs + 1e-8))
    
    # Normalize by maximum possible entropy
    max_entropy = np.log(len(skill_usage))
    
    if max_entropy > 0:
        diversity = entropy / max_entropy
    else:
        diversity = 0.0
    
    return diversity


def compute_communication_efficiency(
    attention_weights: torch.Tensor,
    communication_range: Optional[float] = None
) -> float:
    """
    Compute communication efficiency based on attention patterns.
    
    Args:
        attention_weights: Attention weights [batch_size, num_heads, num_agents]
        communication_range: Optional communication range for efficiency calculation
        
    Returns:
        Communication efficiency score
    """
    if attention_weights is None:
        return 0.0
    
    # Compute attention entropy
    attention_probs = torch.softmax(attention_weights, dim=-1)
    entropy = -torch.sum(attention_probs * torch.log(attention_probs + 1e-8), dim=-1)
    
    # Average over batch and heads
    avg_entropy = entropy.mean().item()
    
    # Normalize by maximum entropy
    num_agents = attention_weights.size(-1)
    max_entropy = math.log(num_agents)
    
    if max_entropy > 0:
        efficiency = 1.0 - (avg_entropy / max_entropy)
    else:
        efficiency = 0.0
    
    return efficiency


def compute_episode_metrics(episode_data: Dict[str, Any]) -> Dict[str, float]:
    """
    Compute comprehensive metrics for an episode.
    
    Args:
        episode_data: Episode data dictionary
        
    Returns:
        Dictionary of computed metrics
    """
    metrics = {}
    
    # Basic episode metrics
    metrics["episode_reward"] = episode_data["episode_reward"]
    metrics["episode_length"] = episode_data["episode_length"]
    
    # Reward statistics
    rewards = np.array(episode_data["rewards"])
    if len(rewards) > 0:
        metrics["mean_step_reward"] = np.mean(rewards)
        metrics["std_step_reward"] = np.std(rewards)
        metrics["min_step_reward"] = np.min(rewards)
        metrics["max_step_reward"] = np.max(rewards)
    
    # Action statistics
    actions = np.array(episode_data["actions"])
    if len(actions) > 0:
        metrics["mean_action_magnitude"] = np.mean(np.linalg.norm(actions, axis=-1))
        metrics["action_variance"] = np.var(actions)
    
    # Communication metrics (if available)
    infos = episode_data.get("infos", [])
    if infos and len(infos) > 0:
        # Extract communication info
        attention_entropies = []
        message_norms = []
        
        for step_infos in infos:
            for agent_info in step_infos:
                if "attention_entropy" in agent_info:
                    attention_entropies.append(agent_info["attention_entropy"])
                if "message_norm" in agent_info:
                    message_norms.append(agent_info["message_norm"])
        
        if attention_entropies:
            metrics["mean_attention_entropy"] = np.mean(attention_entropies)
            metrics["std_attention_entropy"] = np.std(attention_entropies)
        
        if message_norms:
            metrics["mean_message_norm"] = np.mean(message_norms)
            metrics["std_message_norm"] = np.std(message_norms)
    
    return metrics


def compute_training_metrics(
    episode_rewards: List[float],
    episode_lengths: List[int],
    training_losses: List[float]
) -> Dict[str, float]:
    """
    Compute training progress metrics.
    
    Args:
        episode_rewards: List of episode rewards
        episode_lengths: List of episode lengths
        training_losses: List of training losses
        
    Returns:
        Dictionary of training metrics
    """
    metrics = {}
    
    if episode_rewards:
        metrics["mean_episode_reward"] = np.mean(episode_rewards)
        metrics["std_episode_reward"] = np.std(episode_rewards)
        metrics["min_episode_reward"] = np.min(episode_rewards)
        metrics["max_episode_reward"] = np.max(episode_rewards)
    
    if episode_lengths:
        metrics["mean_episode_length"] = np.mean(episode_lengths)
        metrics["std_episode_length"] = np.std(episode_lengths)
    
    if training_losses:
        metrics["mean_training_loss"] = np.mean(training_losses)
        metrics["std_training_loss"] = np.std(training_losses)
    
    return metrics


class PerformanceMonitor:
    """Monitor for tracking performance trends and detecting convergence."""
    
    def __init__(self, patience: int = 50, min_delta: float = 0.01):
        """
        Initialize performance monitor.
        
        Args:
            patience: Number of steps to wait for improvement
            min_delta: Minimum change to qualify as improvement
        """
        self.patience = patience
        self.min_delta = min_delta
        
        self.best_performance = -np.inf
        self.steps_without_improvement = 0
        self.performance_history = []
    
    def update(self, performance: float) -> bool:
        """
        Update performance monitor.
        
        Args:
            performance: Current performance value
            
        Returns:
            True if training should stop (converged)
        """
        self.performance_history.append(performance)
        
        if performance > self.best_performance + self.min_delta:
            self.best_performance = performance
            self.steps_without_improvement = 0
        else:
            self.steps_without_improvement += 1
        
        return self.steps_without_improvement >= self.patience
    
    def get_stats(self) -> Dict[str, Any]:
        """Get performance monitoring statistics."""
        return {
            "best_performance": self.best_performance,
            "steps_without_improvement": self.steps_without_improvement,
            "converged": self.steps_without_improvement >= self.patience,
            "performance_history": self.performance_history.copy()
        }
