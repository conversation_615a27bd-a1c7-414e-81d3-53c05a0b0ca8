"""
Main entry point for training and evaluation.

This script provides a unified interface for running experiments,
training models, and evaluating performance.
"""

import argparse
import logging
import os
import sys
from pathlib import Path
from typing import Dict, Any

import torch
import yaml
from omegaconf import OmegaConf

from src.utils.logger import setup_logger
from src.utils.seed_manager import set_global_seed
from src.trainers.hierarchical_trainer import HierarchicalTrainer
from src.environments.environment_loader import load_environment
from src.agents.hierarchical_agent import HierarchicalAgent


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Scalable Decentralized Hierarchical Reinforcement Learning"
    )
    
    # Main arguments
    parser.add_argument(
        "--config", 
        type=str, 
        default="configs/config.yaml",
        help="Path to configuration file"
    )
    parser.add_argument(
        "--mode", 
        choices=["train", "eval", "sweep", "debug"],
        default="train",
        help="Execution mode"
    )
    
    # Override arguments
    parser.add_argument("--seed", type=int, help="Random seed")
    parser.add_argument("--device", type=str, help="Device to use (cpu/cuda)")
    parser.add_argument("--num-agents", type=int, help="Number of agents")
    parser.add_argument("--env-name", type=str, help="Environment name")
    
    # Training arguments
    parser.add_argument("--total-timesteps", type=int, help="Total training timesteps")
    parser.add_argument("--learning-rate", type=float, help="Learning rate")
    parser.add_argument("--batch-size", type=int, help="Batch size")
    
    # Evaluation arguments
    parser.add_argument("--checkpoint", type=str, help="Path to checkpoint for evaluation")
    parser.add_argument("--eval-episodes", type=int, help="Number of evaluation episodes")
    parser.add_argument("--render", action="store_true", help="Render during evaluation")
    
    # Logging arguments
    parser.add_argument("--log-level", type=str, default="INFO", help="Logging level")
    parser.add_argument("--wandb", action="store_true", help="Use Weights & Biases logging")
    parser.add_argument("--tensorboard", action="store_true", help="Use TensorBoard logging")
    
    # Debug arguments
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    parser.add_argument("--profile", action="store_true", help="Enable profiling")
    
    return parser.parse_args()


def load_config(config_path: str, args: argparse.Namespace) -> Dict[str, Any]:
    """Load and merge configuration from file and command line arguments."""
    # Load base configuration
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Convert to OmegaConf for easier manipulation
    config = OmegaConf.create(config)
    
    # Override with command line arguments
    if args.seed is not None:
        config.experiment.seed = args.seed
    if args.device is not None:
        config.experiment.device = args.device
    if args.num_agents is not None:
        config.environment.num_agents = args.num_agents
    if args.env_name is not None:
        config.environment.name = args.env_name
    if args.total_timesteps is not None:
        config.training.total_timesteps = args.total_timesteps
    if args.learning_rate is not None:
        config.training.learning_rate = args.learning_rate
    if args.batch_size is not None:
        config.training.batch_size = args.batch_size
    if args.eval_episodes is not None:
        config.training.eval_episodes = args.eval_episodes
    if args.wandb:
        config.logging.wandb = True
    if args.tensorboard:
        config.logging.tensorboard = True
    if args.debug:
        config.debug.enabled = True
    if args.profile:
        config.debug.profile = True
    
    return OmegaConf.to_container(config, resolve=True)


def setup_device(config: Dict[str, Any]) -> torch.device:
    """Setup and return the appropriate device."""
    device_str = config["experiment"]["device"]
    
    if device_str == "auto":
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    else:
        device = torch.device(device_str)
    
    logging.info(f"Using device: {device}")
    return device


def create_directories(config: Dict[str, Any]) -> None:
    """Create necessary directories for logging and checkpoints."""
    save_dir = Path(config["experiment"]["save_dir"])
    log_dir = Path(config["experiment"]["log_dir"])
    
    save_dir.mkdir(parents=True, exist_ok=True)
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Create experiment-specific subdirectories
    exp_name = config["experiment"]["name"]
    (save_dir / exp_name).mkdir(exist_ok=True)
    (log_dir / exp_name).mkdir(exist_ok=True)


def train(config: Dict[str, Any], device: torch.device) -> None:
    """Run training."""
    logging.info("Starting training...")
    
    # Create environment
    env = load_environment(config["environment"])
    
    # Create agent
    agent = HierarchicalAgent(config["agent"], env.observation_space, env.action_space)
    
    # Create trainer
    trainer = HierarchicalTrainer(
        agent=agent,
        env=env,
        config=config,
        device=device
    )
    
    # Start training
    trainer.train()
    
    logging.info("Training completed!")


def evaluate(config: Dict[str, Any], device: torch.device, checkpoint_path: str) -> None:
    """Run evaluation."""
    logging.info(f"Starting evaluation with checkpoint: {checkpoint_path}")
    
    # Create environment
    env = load_environment(config["environment"])
    
    # Create agent and load checkpoint
    agent = HierarchicalAgent(config["agent"], env.observation_space, env.action_space)
    agent.load_checkpoint(checkpoint_path)
    
    # Create trainer for evaluation
    trainer = HierarchicalTrainer(
        agent=agent,
        env=env,
        config=config,
        device=device
    )
    
    # Run evaluation
    results = trainer.evaluate(num_episodes=config["training"]["eval_episodes"])
    
    logging.info(f"Evaluation results: {results}")


def main() -> None:
    """Main function."""
    args = parse_args()
    
    # Load configuration
    config = load_config(args.config, args)
    
    # Setup logging
    setup_logger(
        level=args.log_level,
        log_dir=config["experiment"]["log_dir"],
        experiment_name=config["experiment"]["name"]
    )
    
    # Set random seed
    set_global_seed(config["experiment"]["seed"])
    
    # Setup device
    device = setup_device(config)
    
    # Create directories
    create_directories(config)
    
    # Log configuration
    logging.info(f"Configuration: {OmegaConf.to_yaml(OmegaConf.create(config))}")
    
    try:
        if args.mode == "train":
            train(config, device)
        elif args.mode == "eval":
            if not args.checkpoint:
                raise ValueError("Checkpoint path required for evaluation mode")
            evaluate(config, device, args.checkpoint)
        elif args.mode == "sweep":
            logging.info("Sweep mode not implemented yet")
        elif args.mode == "debug":
            logging.info("Debug mode not implemented yet")
        else:
            raise ValueError(f"Unknown mode: {args.mode}")
            
    except Exception as e:
        logging.error(f"Error during execution: {e}")
        if args.debug:
            raise
        sys.exit(1)


if __name__ == "__main__":
    main()
