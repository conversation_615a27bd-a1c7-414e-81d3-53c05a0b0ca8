"""
Unit tests for communication modules.

Tests attention-based communication, mean pooling baseline, and communication utilities.
"""

import pytest
import torch
import torch.nn as nn
import numpy as np
from unittest.mock import Mock, patch

from src.communication.attention_comm import AttentionCommunication, MultiAgentAttentionCommunication


class TestAttentionCommunication:
    """Test cases for AttentionCommunication."""
    
    @pytest.fixture
    def comm_config(self):
        """Communication configuration for testing."""
        return {
            "embed_dim": 32,
            "num_heads": 4,
            "dropout": 0.1,
            "max_agents": 8,
            "message_dim": 16,
            "range_limit": None
        }
    
    @pytest.fixture
    def attention_comm(self, comm_config):
        """Create attention communication module."""
        return AttentionCommunication(comm_config, obs_dim=10)
    
    def test_attention_comm_initialization(self, attention_comm, comm_config):
        """Test attention communication initialization."""
        assert attention_comm.obs_dim == 10
        assert attention_comm.embed_dim == comm_config["embed_dim"]
        assert attention_comm.num_heads == comm_config["num_heads"]
        assert attention_comm.message_dim == comm_config["message_dim"]
        assert attention_comm.range_limit is None
    
    def test_attention_comm_networks(self, attention_comm):
        """Test that all networks are properly created."""
        assert hasattr(attention_comm, 'obs_encoder')
        assert hasattr(attention_comm, 'attention_module')
        assert hasattr(attention_comm, 'message_decoder')
        
        # Check network types
        assert isinstance(attention_comm.obs_encoder, nn.Sequential)
        assert isinstance(attention_comm.message_decoder, nn.Sequential)
    
    def test_forward_no_other_agents(self, attention_comm):
        """Test forward pass with no other agents."""
        batch_size = 2
        obs_dim = 10
        
        own_obs = torch.randn(batch_size, obs_dim)
        other_obs = []  # No other agents
        
        communicated_obs, info = attention_comm(own_obs, other_obs)
        
        # Should return original observation when no communication
        torch.testing.assert_close(communicated_obs, own_obs)
        assert info["num_communicating_agents"] == 0
        assert info["attention_weights"] is None
    
    def test_forward_with_other_agents(self, attention_comm):
        """Test forward pass with other agents."""
        batch_size = 2
        obs_dim = 10
        num_other_agents = 3
        
        own_obs = torch.randn(batch_size, obs_dim)
        other_obs = [torch.randn(batch_size, obs_dim) for _ in range(num_other_agents)]
        
        communicated_obs, info = attention_comm(own_obs, other_obs)
        
        assert communicated_obs.shape == (batch_size, obs_dim)
        assert info["num_communicating_agents"] == num_other_agents
        assert "attention_weights" in info
        assert "attention_entropy" in info
        assert "communication_efficiency" in info
        assert "message_norm" in info
    
    def test_forward_with_positions(self, attention_comm):
        """Test forward pass with position-based range limiting."""
        # Set range limit
        attention_comm.set_range_limit(1.0)
        
        batch_size = 2
        obs_dim = 10
        num_agents = 4  # Including own agent
        
        own_obs = torch.randn(batch_size, obs_dim)
        other_obs = [torch.randn(batch_size, obs_dim) for _ in range(num_agents - 1)]
        
        # Create positions (own agent at origin, others at various distances)
        positions = torch.tensor([
            [[0.0, 0.0], [0.5, 0.0], [2.0, 0.0], [0.0, 0.5]],  # Batch 1
            [[0.0, 0.0], [0.3, 0.4], [1.5, 0.0], [0.0, 0.8]]   # Batch 2
        ])
        
        communicated_obs, info = attention_comm(own_obs, other_obs, positions)
        
        assert communicated_obs.shape == (batch_size, obs_dim)
        assert info["communication_range"] == 1.0
    
    def test_range_mask_computation(self, attention_comm):
        """Test communication range mask computation."""
        batch_size = 2
        num_agents = 4
        
        # Positions: own agent at origin, others at various distances
        positions = torch.tensor([
            [[0.0, 0.0], [0.5, 0.0], [2.0, 0.0], [0.0, 0.5]],  # Batch 1
            [[0.0, 0.0], [0.3, 0.4], [1.5, 0.0], [0.0, 0.8]]   # Batch 2
        ])
        
        range_limit = 1.0
        mask = attention_comm._compute_range_mask(positions, range_limit)
        
        assert mask.shape == (batch_size, 1, num_agents - 1)
        
        # Check that close agents are not masked (True) and far agents are masked (False)
        # Agent 1: distance 0.5 (should be True)
        # Agent 2: distance 2.0 (should be False)  
        # Agent 3: distance 0.5 (should be True)
        assert mask[0, 0, 0] == True   # Agent 1, close
        assert mask[0, 0, 1] == False  # Agent 2, far
        assert mask[0, 0, 2] == True   # Agent 3, close
    
    def test_communication_stats(self, attention_comm):
        """Test communication statistics tracking."""
        # Initially no stats
        stats = attention_comm.get_communication_stats()
        assert stats == {}
        
        # Set training mode and perform communication
        attention_comm.train()
        
        batch_size = 2
        obs_dim = 10
        own_obs = torch.randn(batch_size, obs_dim)
        other_obs = [torch.randn(batch_size, obs_dim) for _ in range(2)]
        
        # Perform several communication steps
        for _ in range(3):
            attention_comm(own_obs, other_obs)
        
        # Check stats
        stats = attention_comm.get_communication_stats()
        assert "avg_message_norm" in stats
        assert "total_communications" in stats
        assert stats["total_communications"] == 3
    
    def test_reset_communication(self, attention_comm):
        """Test communication reset functionality."""
        # Add some history
        attention_comm.communication_history = [{"test": "data"}]
        
        attention_comm.reset()
        
        assert len(attention_comm.communication_history) == 0
    
    def test_set_range_limit(self, attention_comm):
        """Test setting communication range limit."""
        # Initially no limit
        assert attention_comm.range_limit is None
        
        # Set limit
        attention_comm.set_range_limit(2.5)
        assert attention_comm.range_limit == 2.5
        
        # Remove limit
        attention_comm.set_range_limit(None)
        assert attention_comm.range_limit is None
    
    def test_get_attention_patterns(self, attention_comm):
        """Test getting attention patterns."""
        # Initially no patterns
        patterns = attention_comm.get_attention_patterns()
        assert patterns is None
        
        # Perform communication to generate patterns
        attention_comm.train()
        batch_size = 2
        obs_dim = 10
        own_obs = torch.randn(batch_size, obs_dim)
        other_obs = [torch.randn(batch_size, obs_dim) for _ in range(2)]
        
        attention_comm(own_obs, other_obs)
        
        # Should now have patterns (though they might be None if not requested)
        patterns = attention_comm.get_attention_patterns()
        # The actual content depends on implementation details
    
    def test_communication_logging_toggle(self, attention_comm):
        """Test enabling/disabling communication logging."""
        # Add some history
        attention_comm.communication_history = [{"test": "data"}]
        
        # Disable logging (should clear history)
        attention_comm.enable_communication_logging(False)
        assert len(attention_comm.communication_history) == 0
        
        # Enable logging
        attention_comm.enable_communication_logging(True)
        # Should be ready to log again


class TestMultiAgentAttentionCommunication:
    """Test cases for MultiAgentAttentionCommunication."""
    
    @pytest.fixture
    def comm_config(self):
        """Communication configuration."""
        return {
            "embed_dim": 16,
            "num_heads": 2,
            "dropout": 0.1,
            "max_agents": 5,
            "message_dim": 8
        }
    
    @pytest.fixture
    def multi_agent_comm(self, comm_config):
        """Create multi-agent communication system."""
        return MultiAgentAttentionCommunication(comm_config, obs_dim=6, num_agents=3)
    
    def test_multi_agent_initialization(self, multi_agent_comm, comm_config):
        """Test multi-agent communication initialization."""
        assert multi_agent_comm.num_agents == 3
        assert multi_agent_comm.obs_dim == 6
        assert len(multi_agent_comm.comm_modules) == 3
        
        # Check that each module is properly initialized
        for module in multi_agent_comm.comm_modules:
            assert isinstance(module, AttentionCommunication)
    
    def test_multi_agent_forward(self, multi_agent_comm):
        """Test multi-agent communication forward pass."""
        batch_size = 2
        num_agents = 3
        obs_dim = 6
        
        # All agent observations
        observations = torch.randn(batch_size, num_agents, obs_dim)
        
        communicated_obs, info_list = multi_agent_comm(observations)
        
        assert communicated_obs.shape == (batch_size, num_agents, obs_dim)
        assert len(info_list) == num_agents
        
        # Check that each agent has communication info
        for info in info_list:
            assert isinstance(info, dict)
            assert "num_communicating_agents" in info
            assert info["num_communicating_agents"] == num_agents - 1  # Excludes self
    
    def test_multi_agent_with_positions(self, multi_agent_comm):
        """Test multi-agent communication with positions."""
        batch_size = 2
        num_agents = 3
        obs_dim = 6
        
        observations = torch.randn(batch_size, num_agents, obs_dim)
        positions = torch.randn(batch_size, num_agents, 2)
        
        communicated_obs, info_list = multi_agent_comm(observations, positions)
        
        assert communicated_obs.shape == (batch_size, num_agents, obs_dim)
        assert len(info_list) == num_agents
    
    def test_multi_agent_reset(self, multi_agent_comm):
        """Test multi-agent communication reset."""
        # This should not raise any errors
        multi_agent_comm.reset()
        
        # Check that all individual modules are reset
        for module in multi_agent_comm.comm_modules:
            assert len(module.communication_history) == 0
    
    def test_global_communication_stats(self, multi_agent_comm):
        """Test global communication statistics."""
        # Initially no stats
        stats = multi_agent_comm.get_global_communication_stats()
        assert stats == {}
        
        # Perform communication to generate stats
        multi_agent_comm.train()
        
        batch_size = 2
        num_agents = 3
        obs_dim = 6
        
        observations = torch.randn(batch_size, num_agents, obs_dim)
        
        # Perform several communication steps
        for _ in range(2):
            multi_agent_comm(observations)
        
        # Get global stats
        stats = multi_agent_comm.get_global_communication_stats()
        
        # Should have aggregated statistics
        if stats:  # Only check if stats were generated
            assert any(key.startswith("avg_") for key in stats.keys())


class TestCommunicationIntegration:
    """Integration tests for communication components."""
    
    def test_attention_communication_end_to_end(self):
        """Test complete attention communication workflow."""
        config = {
            "embed_dim": 24,
            "num_heads": 3,
            "dropout": 0.0,  # Disable dropout for deterministic testing
            "max_agents": 6,
            "message_dim": 12,
            "range_limit": 2.0
        }
        
        comm = AttentionCommunication(config, obs_dim=8)
        comm.eval()  # Set to eval mode for deterministic behavior
        
        batch_size = 1
        obs_dim = 8
        num_other_agents = 2
        
        # Create consistent inputs
        torch.manual_seed(42)
        own_obs = torch.randn(batch_size, obs_dim)
        other_obs = [torch.randn(batch_size, obs_dim) for _ in range(num_other_agents)]
        positions = torch.tensor([[[0.0, 0.0], [1.0, 0.0], [3.0, 0.0]]])  # One agent out of range
        
        # Perform communication
        communicated_obs, info = comm(own_obs, other_obs, positions)
        
        # Verify outputs
        assert communicated_obs.shape == (batch_size, obs_dim)
        assert not torch.equal(communicated_obs, own_obs)  # Should be modified
        assert info["num_communicating_agents"] == num_other_agents
        assert info["communication_range"] == 2.0
        assert isinstance(info["message_norm"], float)
        assert info["message_norm"] >= 0
    
    def test_communication_with_different_batch_sizes(self):
        """Test communication with various batch sizes."""
        config = {
            "embed_dim": 16,
            "num_heads": 2,
            "dropout": 0.1,
            "max_agents": 4,
            "message_dim": 8
        }
        
        comm = AttentionCommunication(config, obs_dim=6)
        
        # Test different batch sizes
        for batch_size in [1, 3, 8]:
            own_obs = torch.randn(batch_size, 6)
            other_obs = [torch.randn(batch_size, 6) for _ in range(2)]
            
            communicated_obs, info = comm(own_obs, other_obs)
            
            assert communicated_obs.shape == (batch_size, 6)
            assert info["num_communicating_agents"] == 2
    
    def test_communication_gradient_flow(self):
        """Test that gradients flow through communication module."""
        config = {
            "embed_dim": 16,
            "num_heads": 2,
            "dropout": 0.0,
            "max_agents": 4,
            "message_dim": 8
        }
        
        comm = AttentionCommunication(config, obs_dim=4)
        
        batch_size = 2
        own_obs = torch.randn(batch_size, 4, requires_grad=True)
        other_obs = [torch.randn(batch_size, 4, requires_grad=True) for _ in range(2)]
        
        communicated_obs, _ = comm(own_obs, other_obs)
        
        # Compute a simple loss
        loss = communicated_obs.sum()
        loss.backward()
        
        # Check that gradients exist
        assert own_obs.grad is not None
        for obs in other_obs:
            assert obs.grad is not None
        
        # Check that communication module parameters have gradients
        for param in comm.parameters():
            if param.requires_grad:
                assert param.grad is not None
    
    def test_communication_device_consistency(self):
        """Test that communication works consistently across devices."""
        config = {
            "embed_dim": 16,
            "num_heads": 2,
            "dropout": 0.1,
            "max_agents": 4,
            "message_dim": 8
        }
        
        comm = AttentionCommunication(config, obs_dim=4)
        
        # Test on CPU
        device = torch.device('cpu')
        comm.to(device)
        
        batch_size = 2
        own_obs = torch.randn(batch_size, 4, device=device)
        other_obs = [torch.randn(batch_size, 4, device=device) for _ in range(2)]
        
        communicated_obs, info = comm(own_obs, other_obs)
        
        assert communicated_obs.device == device
        assert communicated_obs.shape == (batch_size, 4)


if __name__ == "__main__":
    pytest.main([__file__])
