"""
Hierarchical trainer for multi-agent reinforcement learning.

This module implements the training loop for hierarchical agents
with attention-based communication.
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from typing import Dict, Any, List, Optional
import time

from .base_trainer import BaseTrainer
from src.algorithms.ppo import PPOAlgorithm
from src.utils.metrics import compute_episode_metrics


class HierarchicalTrainer(BaseTrainer):
    """Trainer for hierarchical multi-agent reinforcement learning."""
    
    def __init__(
        self,
        agent,
        env,
        config: Dict[str, Any],
        device: torch.device
    ):
        """
        Initialize hierarchical trainer.
        
        Args:
            agent: Hierarchical agent to train
            env: Multi-agent environment
            config: Training configuration
            device: Device for computations
        """
        super().__init__(agent, env, config, device)
        
        # Training algorithm
        self.algorithm = config["training"]["algorithm"]
        
        # Initialize RL algorithm
        if self.algorithm == "ppo":
            self.rl_algorithm = PPOAlgorithm(
                agent=agent,
                config=config["training"],
                device=device
            )
        else:
            raise ValueError(f"Unknown algorithm: {self.algorithm}")
        
        # Experience collection
        self.batch_size = config["training"]["batch_size"]
        self.experience_buffer = []
        
        # Training metrics
        self.training_metrics = {}
        
    def train(self) -> None:
        """Main training loop."""
        self.logger.log_experiment_start(self.config)
        
        try:
            while self.step_count < self.total_timesteps:
                # Collect experience
                episode_data = self.collect_episode()
                
                # Update metrics
                episode_metrics = compute_episode_metrics(episode_data)
                self.log_training_step(episode_metrics)
                
                # Add to experience buffer
                self.experience_buffer.append(episode_data)
                
                # Update step count
                self.step_count += episode_data["episode_length"]
                self.episode_count += 1
                
                # Train agent when buffer is full
                if len(self.experience_buffer) >= self.batch_size:
                    training_metrics = self.train_step()
                    self.log_training_step(training_metrics)
                    self.experience_buffer.clear()
                
                # Evaluation
                if self.should_evaluate():
                    eval_metrics = self.evaluate()
                    self.log_evaluation(eval_metrics)
                
                # Save checkpoint
                if self.should_save_checkpoint():
                    self.save_checkpoint()
                    
        except KeyboardInterrupt:
            self.logger.logger.info("Training interrupted by user")
        except Exception as e:
            self.logger.logger.error(f"Training failed: {e}")
            raise
        finally:
            self.cleanup()
    
    def train_step(self) -> Dict[str, float]:
        """
        Perform one training step using collected experience.
        
        Returns:
            Training metrics
        """
        if not self.experience_buffer:
            return {}
        
        # Convert experience buffer to training format
        training_data = self._prepare_training_data()
        
        # Update agent using RL algorithm
        training_metrics = self.rl_algorithm.update(training_data)
        
        return training_metrics
    
    def _prepare_training_data(self) -> Dict[str, torch.Tensor]:
        """
        Prepare training data from experience buffer.
        
        Returns:
            Training data dictionary
        """
        # Collect all experiences
        all_observations = []
        all_actions = []
        all_rewards = []
        all_dones = []
        all_values = []
        all_log_probs = []
        
        for episode_data in self.experience_buffer:
            observations = episode_data["observations"]
            actions = episode_data["actions"]
            rewards = episode_data["rewards"]
            dones = episode_data["dones"]
            
            # Flatten episode data
            for step_idx in range(len(observations)):
                for agent_idx in range(len(observations[step_idx])):
                    all_observations.append(observations[step_idx][agent_idx])
                    all_actions.append(actions[step_idx][agent_idx])
                    all_rewards.append(rewards[step_idx][agent_idx])
                    all_dones.append(dones[step_idx][agent_idx])
        
        # Convert to tensors
        training_data = {
            "observations": torch.FloatTensor(np.array(all_observations)).to(self.device),
            "actions": torch.FloatTensor(np.array(all_actions)).to(self.device),
            "rewards": torch.FloatTensor(np.array(all_rewards)).to(self.device),
            "dones": torch.FloatTensor(np.array(all_dones)).to(self.device)
        }
        
        return training_data
    
    def evaluate(self, num_episodes: int = None) -> Dict[str, float]:
        """
        Evaluate agent performance.
        
        Args:
            num_episodes: Number of episodes to evaluate
            
        Returns:
            Evaluation metrics
        """
        if num_episodes is None:
            num_episodes = self.eval_episodes
        
        self.agent.eval()
        
        episode_rewards = []
        episode_lengths = []
        coordination_scores = []
        
        for _ in range(num_episodes):
            episode_data = self.collect_episode(deterministic=True)
            
            episode_rewards.append(episode_data["episode_reward"])
            episode_lengths.append(episode_data["episode_length"])
            
            # Compute additional metrics
            episode_metrics = compute_episode_metrics(episode_data)
            if "coordination_score" in episode_metrics:
                coordination_scores.append(episode_metrics["coordination_score"])
        
        self.agent.train()
        
        # Compute evaluation metrics
        eval_metrics = {
            "episode_reward": np.mean(episode_rewards),
            "episode_reward_std": np.std(episode_rewards),
            "episode_length": np.mean(episode_lengths),
            "episode_length_std": np.std(episode_lengths)
        }
        
        if coordination_scores:
            eval_metrics["coordination_score"] = np.mean(coordination_scores)
            eval_metrics["coordination_score_std"] = np.std(coordination_scores)
        
        return eval_metrics
    
    def collect_episode(self, deterministic: bool = False) -> Dict[str, Any]:
        """
        Collect episode with hierarchical agent.
        
        Args:
            deterministic: Whether to use deterministic policy
            
        Returns:
            Episode data
        """
        # Reset agent for new episode
        self.agent.reset()
        
        # Use parent method but with hierarchical-specific processing
        episode_data = super().collect_episode(deterministic)
        
        # Add hierarchical-specific metrics
        self._add_hierarchical_metrics(episode_data)
        
        return episode_data
    
    def _add_hierarchical_metrics(self, episode_data: Dict[str, Any]) -> None:
        """Add hierarchical-specific metrics to episode data."""
        infos = episode_data.get("infos", [])
        
        if not infos:
            return
        
        # Extract skill usage statistics
        skill_usage = {}
        attention_entropies = []
        communication_efficiencies = []
        
        for step_infos in infos:
            for agent_info in step_infos:
                # Skill usage
                if "current_skill" in agent_info:
                    skill = agent_info["current_skill"]
                    skill_usage[skill] = skill_usage.get(skill, 0) + 1
                
                # Attention metrics
                if "attention_entropy" in agent_info:
                    attention_entropies.append(agent_info["attention_entropy"])
                
                if "communication_efficiency" in agent_info:
                    communication_efficiencies.append(agent_info["communication_efficiency"])
        
        # Add to episode data
        episode_data["skill_usage"] = skill_usage
        episode_data["attention_entropies"] = attention_entropies
        episode_data["communication_efficiencies"] = communication_efficiencies
    
    def get_training_stats(self) -> Dict[str, Any]:
        """Get comprehensive training statistics."""
        base_stats = super().get_training_stats()
        
        # Add hierarchical-specific stats
        hierarchical_stats = {
            "algorithm": self.algorithm,
            "experience_buffer_size": len(self.experience_buffer),
            "training_metrics": self.training_metrics.copy()
        }
        
        # Merge stats
        base_stats.update(hierarchical_stats)
        
        return base_stats
