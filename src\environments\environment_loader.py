"""
Environment loader for creating and configuring multi-agent environments.

This module provides a unified interface for loading different types
of multi-agent environments with consistent configuration.
"""

import gym
from gym import spaces
import numpy as np
from typing import Dict, Any, Tuple, List, Optional
import logging

try:
    from pettingzoo.mpe import simple_spread_v2, simple_tag_v2, simple_adversary_v2
    MPE_AVAILABLE = True
except ImportError:
    MPE_AVAILABLE = False
    logging.warning("PettingZoo MPE environments not available")

from .env_utils import get_env_info


class MultiAgentEnvWrapper:
    """Wrapper for multi-agent environments to provide consistent interface."""
    
    def __init__(self, env, config: Dict[str, Any]):
        """
        Initialize environment wrapper.
        
        Args:
            env: Base environment
            config: Environment configuration
        """
        self.env = env
        self.config = config
        self.num_agents = config.get("num_agents", 3)
        
        # Environment properties
        self._setup_spaces()
        
        # Episode tracking
        self.episode_step = 0
        self.max_episode_steps = config.get("max_episode_steps", 100)
        
        # Rendering
        self.render_mode = config.get("render", False)
        
    def _setup_spaces(self):
        """Setup observation and action spaces."""
        if hasattr(self.env, 'observation_spaces'):
            # PettingZoo style
            agent_names = list(self.env.observation_spaces.keys())
            self.observation_space = self.env.observation_spaces[agent_names[0]]
            self.action_space = self.env.action_spaces[agent_names[0]]
        else:
            # Custom environment
            self.observation_space = self.env.observation_space
            self.action_space = self.env.action_space
    
    def reset(self) -> List[np.ndarray]:
        """
        Reset environment and return initial observations.
        
        Returns:
            List of initial observations for each agent
        """
        self.episode_step = 0
        
        if hasattr(self.env, 'reset'):
            obs = self.env.reset()
            if isinstance(obs, dict):
                # PettingZoo style - convert to list
                agent_names = sorted(obs.keys())
                return [obs[name] for name in agent_names]
            else:
                return obs
        else:
            raise NotImplementedError("Environment must implement reset method")
    
    def step(self, actions: List[np.ndarray]) -> Tuple[List[np.ndarray], List[float], List[bool], Dict[str, Any]]:
        """
        Take environment step with actions from all agents.
        
        Args:
            actions: List of actions for each agent
            
        Returns:
            Tuple of (observations, rewards, dones, info)
        """
        self.episode_step += 1
        
        if hasattr(self.env, 'step'):
            # Convert actions to appropriate format
            if hasattr(self.env, 'action_spaces'):
                # PettingZoo style
                agent_names = sorted(self.env.action_spaces.keys())
                action_dict = {name: actions[i] for i, name in enumerate(agent_names)}
                obs, rewards, dones, infos = self.env.step(action_dict)
                
                # Convert back to lists
                obs_list = [obs[name] for name in agent_names]
                reward_list = [rewards[name] for name in agent_names]
                done_list = [dones[name] for name in agent_names]
                info_dict = {"agents": infos}
            else:
                # Custom environment
                obs_list, reward_list, done_list, info_dict = self.env.step(actions)
        else:
            raise NotImplementedError("Environment must implement step method")
        
        # Check episode termination
        episode_done = (self.episode_step >= self.max_episode_steps) or any(done_list)
        if episode_done:
            done_list = [True] * len(done_list)
        
        info_dict["episode_step"] = self.episode_step
        info_dict["episode_done"] = episode_done
        
        return obs_list, reward_list, done_list, info_dict
    
    def render(self, mode: str = "human"):
        """Render environment."""
        if hasattr(self.env, 'render') and self.render_mode:
            return self.env.render(mode=mode)
    
    def close(self):
        """Close environment."""
        if hasattr(self.env, 'close'):
            self.env.close()
    
    def get_env_info(self) -> Dict[str, Any]:
        """Get environment information."""
        return get_env_info(self)


def load_environment(config: Dict[str, Any]) -> MultiAgentEnvWrapper:
    """
    Load environment based on configuration.
    
    Args:
        config: Environment configuration dictionary
        
    Returns:
        Wrapped multi-agent environment
    """
    env_name = config["name"]
    
    if env_name == "mpe_spread":
        env = _load_mpe_spread(config)
    elif env_name == "mpe_tag":
        env = _load_mpe_tag(config)
    elif env_name == "moving_goals":
        env = _load_moving_goals(config)
    elif env_name == "custom_swarm":
        env = _load_custom_swarm(config)
    else:
        raise ValueError(f"Unknown environment: {env_name}")
    
    return MultiAgentEnvWrapper(env, config)


def create_environment(env_name: str, **kwargs) -> MultiAgentEnvWrapper:
    """
    Create environment with default configuration.
    
    Args:
        env_name: Name of environment
        **kwargs: Additional configuration parameters
        
    Returns:
        Wrapped environment
    """
    config = {
        "name": env_name,
        "num_agents": kwargs.get("num_agents", 3),
        "max_episode_steps": kwargs.get("max_episode_steps", 100),
        "render": kwargs.get("render", False),
        **kwargs
    }
    
    return load_environment(config)


def _load_mpe_spread(config: Dict[str, Any]):
    """Load MPE Spread environment."""
    if not MPE_AVAILABLE:
        raise ImportError("PettingZoo MPE environments not available")
    
    num_agents = config.get("num_agents", 3)
    
    env = simple_spread_v2.env(
        N=num_agents,
        local_ratio=0.5,
        max_cycles=config.get("max_episode_steps", 25),
        continuous_actions=True
    )
    
    return env


def _load_mpe_tag(config: Dict[str, Any]):
    """Load MPE Tag environment."""
    if not MPE_AVAILABLE:
        raise ImportError("PettingZoo MPE environments not available")
    
    num_good = config.get("num_good", 1)
    num_adversaries = config.get("num_adversaries", 3)
    
    env = simple_tag_v2.env(
        num_good=num_good,
        num_adversaries=num_adversaries,
        num_obstacles=config.get("num_obstacles", 2),
        max_cycles=config.get("max_episode_steps", 25),
        continuous_actions=True
    )
    
    return env


def _load_moving_goals(config: Dict[str, Any]):
    """Load Moving Goals environment."""
    # This would be a custom environment implementation
    # For now, return a placeholder
    
    class MovingGoalsEnv:
        def __init__(self, config):
            self.config = config
            self.num_agents = config.get("num_agents", 3)
            
            # Define spaces
            obs_dim = 10  # Example observation dimension
            action_dim = 2  # Example action dimension
            
            self.observation_space = spaces.Box(
                low=-np.inf, high=np.inf, shape=(obs_dim,), dtype=np.float32
            )
            self.action_space = spaces.Box(
                low=-1.0, high=1.0, shape=(action_dim,), dtype=np.float32
            )
            
        def reset(self):
            return [np.zeros(self.observation_space.shape) for _ in range(self.num_agents)]
        
        def step(self, actions):
            obs = [np.zeros(self.observation_space.shape) for _ in range(self.num_agents)]
            rewards = [0.0] * self.num_agents
            dones = [False] * self.num_agents
            info = {}
            return obs, rewards, dones, info
        
        def render(self, mode="human"):
            pass
        
        def close(self):
            pass
    
    return MovingGoalsEnv(config)


def _load_custom_swarm(config: Dict[str, Any]):
    """Load Custom Swarm environment."""
    # This would be a custom swarm robotics environment
    # For now, return a placeholder similar to moving goals
    
    class CustomSwarmEnv:
        def __init__(self, config):
            self.config = config
            self.num_agents = config.get("num_agents", 5)
            
            # Define spaces
            obs_dim = 12  # Example observation dimension
            action_dim = 2  # Example action dimension
            
            self.observation_space = spaces.Box(
                low=-np.inf, high=np.inf, shape=(obs_dim,), dtype=np.float32
            )
            self.action_space = spaces.Box(
                low=-1.0, high=1.0, shape=(action_dim,), dtype=np.float32
            )
            
        def reset(self):
            return [np.random.randn(*self.observation_space.shape) for _ in range(self.num_agents)]
        
        def step(self, actions):
            obs = [np.random.randn(*self.observation_space.shape) for _ in range(self.num_agents)]
            rewards = [np.random.randn() for _ in range(self.num_agents)]
            dones = [False] * self.num_agents
            info = {}
            return obs, rewards, dones, info
        
        def render(self, mode="human"):
            pass
        
        def close(self):
            pass
    
    return CustomSwarmEnv(config)
