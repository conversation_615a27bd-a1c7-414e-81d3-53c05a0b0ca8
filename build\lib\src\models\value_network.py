"""
Value network implementations for critic functions.

This module provides value networks for estimating state values
in both hierarchical and flat policy settings.
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, Any


class ValueNetwork(nn.Module):
    """Value network for state value estimation."""
    
    def __init__(
        self,
        obs_dim: int,
        hidden_dims: list = [128, 64],
        activation: str = "relu",
        config: Dict[str, Any] = None
    ):
        """
        Initialize value network.
        
        Args:
            obs_dim: Observation dimension
            hidden_dims: Hidden layer dimensions
            activation: Activation function name
            config: Additional configuration
        """
        super().__init__()
        
        if config is not None:
            hidden_dims = config.get("hidden_dims", hidden_dims)
            activation = config.get("activation", activation)
        
        self.obs_dim = obs_dim
        
        # Build network
        layers = []
        input_dim = obs_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(input_dim, hidden_dim),
                self._get_activation(activation)
            ])
            input_dim = hidden_dim
        
        # Output layer (single value)
        layers.append(nn.Linear(input_dim, 1))
        
        self.network = nn.Sequential(*layers)
        
        # Initialize weights
        self._init_weights()
    
    def forward(self, obs: torch.Tensor) -> torch.Tensor:
        """
        Forward pass to get state value.
        
        Args:
            obs: Observation tensor [batch_size, obs_dim]
            
        Returns:
            State value [batch_size, 1]
        """
        return self.network(obs)
    
    def _get_activation(self, activation: str) -> nn.Module:
        """Get activation function by name."""
        activations = {
            "relu": nn.ReLU(),
            "tanh": nn.Tanh(),
            "sigmoid": nn.Sigmoid(),
            "leaky_relu": nn.LeakyReLU(),
            "elu": nn.ELU()
        }
        return activations.get(activation, nn.ReLU())
    
    def _init_weights(self) -> None:
        """Initialize network weights."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.orthogonal_(module.weight, gain=np.sqrt(2))
                nn.init.constant_(module.bias, 0.0)


class HierarchicalValueNetwork(nn.Module):
    """Value network for hierarchical policies with skill conditioning."""
    
    def __init__(
        self,
        obs_dim: int,
        num_skills: int,
        hidden_dims: list = [128, 64],
        activation: str = "relu",
        skill_embedding_dim: int = 16
    ):
        """
        Initialize hierarchical value network.
        
        Args:
            obs_dim: Observation dimension
            num_skills: Number of skills
            hidden_dims: Hidden layer dimensions
            activation: Activation function
            skill_embedding_dim: Skill embedding dimension
        """
        super().__init__()
        
        self.obs_dim = obs_dim
        self.num_skills = num_skills
        self.skill_embedding_dim = skill_embedding_dim
        
        # Skill embedding
        self.skill_embedding = nn.Embedding(num_skills, skill_embedding_dim)
        
        # Value network
        input_dim = obs_dim + skill_embedding_dim
        layers = []
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(input_dim, hidden_dim),
                self._get_activation(activation)
            ])
            input_dim = hidden_dim
        
        # Output layer
        layers.append(nn.Linear(input_dim, 1))
        
        self.network = nn.Sequential(*layers)
        
        # Initialize weights
        self._init_weights()
    
    def forward(self, obs: torch.Tensor, skill: torch.Tensor) -> torch.Tensor:
        """
        Forward pass with skill conditioning.
        
        Args:
            obs: Observation tensor [batch_size, obs_dim]
            skill: Skill index tensor [batch_size]
            
        Returns:
            State value [batch_size, 1]
        """
        # Get skill embedding
        skill_embed = self.skill_embedding(skill)
        
        # Concatenate observation and skill embedding
        input_tensor = torch.cat([obs, skill_embed], dim=-1)
        
        return self.network(input_tensor)
    
    def _get_activation(self, activation: str) -> nn.Module:
        """Get activation function by name."""
        activations = {
            "relu": nn.ReLU(),
            "tanh": nn.Tanh(),
            "sigmoid": nn.Sigmoid(),
            "leaky_relu": nn.LeakyReLU(),
            "elu": nn.ELU()
        }
        return activations.get(activation, nn.ReLU())
    
    def _init_weights(self) -> None:
        """Initialize network weights."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.orthogonal_(module.weight, gain=np.sqrt(2))
                nn.init.constant_(module.bias, 0.0)
            elif isinstance(module, nn.Embedding):
                nn.init.normal_(module.weight, 0.0, 0.1)


class DualValueNetwork(nn.Module):
    """Dual value network for hierarchical RL with separate high and low level critics."""
    
    def __init__(
        self,
        obs_dim: int,
        num_skills: int,
        config: Dict[str, Any] = None
    ):
        """
        Initialize dual value network.
        
        Args:
            obs_dim: Observation dimension
            num_skills: Number of skills
            config: Configuration dictionary
        """
        super().__init__()
        
        if config is None:
            config = {}
        
        # High-level value network (for skill selection)
        self.high_level_value = ValueNetwork(
            obs_dim=obs_dim,
            hidden_dims=config.get("high_hidden_dims", [128, 64]),
            activation=config.get("activation", "relu")
        )
        
        # Low-level value network (skill-conditioned)
        self.low_level_value = HierarchicalValueNetwork(
            obs_dim=obs_dim,
            num_skills=num_skills,
            hidden_dims=config.get("low_hidden_dims", [64, 32]),
            activation=config.get("activation", "relu"),
            skill_embedding_dim=config.get("skill_embedding_dim", 16)
        )
    
    def forward_high(self, obs: torch.Tensor) -> torch.Tensor:
        """Get high-level value."""
        return self.high_level_value(obs)
    
    def forward_low(self, obs: torch.Tensor, skill: torch.Tensor) -> torch.Tensor:
        """Get low-level value."""
        return self.low_level_value(obs, skill)
    
    def forward(self, obs: torch.Tensor, skill: torch.Tensor = None, level: str = "low") -> torch.Tensor:
        """
        Forward pass for specified level.
        
        Args:
            obs: Observation tensor
            skill: Skill tensor (required for low level)
            level: "high" or "low"
            
        Returns:
            Value tensor
        """
        if level == "high":
            return self.forward_high(obs)
        elif level == "low":
            if skill is None:
                raise ValueError("Skill required for low-level value")
            return self.forward_low(obs, skill)
        else:
            raise ValueError(f"Unknown level: {level}")


class CommunicationValueNetwork(nn.Module):
    """Value network that incorporates communication information."""
    
    def __init__(
        self,
        obs_dim: int,
        comm_dim: int,
        hidden_dims: list = [128, 64],
        activation: str = "relu"
    ):
        """
        Initialize communication-aware value network.
        
        Args:
            obs_dim: Observation dimension
            comm_dim: Communication message dimension
            hidden_dims: Hidden layer dimensions
            activation: Activation function
        """
        super().__init__()
        
        self.obs_dim = obs_dim
        self.comm_dim = comm_dim
        
        # Combined input processing
        input_dim = obs_dim + comm_dim
        layers = []
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(input_dim, hidden_dim),
                self._get_activation(activation)
            ])
            input_dim = hidden_dim
        
        # Output layer
        layers.append(nn.Linear(input_dim, 1))
        
        self.network = nn.Sequential(*layers)
        
        # Initialize weights
        self._init_weights()
    
    def forward(self, obs: torch.Tensor, comm_msg: torch.Tensor) -> torch.Tensor:
        """
        Forward pass with communication message.
        
        Args:
            obs: Observation tensor [batch_size, obs_dim]
            comm_msg: Communication message [batch_size, comm_dim]
            
        Returns:
            State value [batch_size, 1]
        """
        # Concatenate observation and communication
        input_tensor = torch.cat([obs, comm_msg], dim=-1)
        return self.network(input_tensor)
    
    def _get_activation(self, activation: str) -> nn.Module:
        """Get activation function by name."""
        activations = {
            "relu": nn.ReLU(),
            "tanh": nn.Tanh(),
            "sigmoid": nn.Sigmoid(),
            "leaky_relu": nn.LeakyReLU(),
            "elu": nn.ELU()
        }
        return activations.get(activation, nn.ReLU())
    
    def _init_weights(self) -> None:
        """Initialize network weights."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.orthogonal_(module.weight, gain=np.sqrt(2))
                nn.init.constant_(module.bias, 0.0)
