"""
Hierarchical policy networks for multi-level decision making.

This module implements:
- High-level policy for skill selection
- Low-level policy for primitive action execution
- Combined hierarchical policy wrapper
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Dict, Any, Optional
import numpy as np


class HighLevelPolicy(nn.Module):
    """High-level policy for skill selection."""
    
    def __init__(
        self,
        obs_dim: int,
        num_skills: int,
        hidden_dims: list = [128, 128],
        activation: str = "relu",
        output_activation: str = "linear"
    ):
        """
        Initialize high-level policy.
        
        Args:
            obs_dim: Observation dimension
            num_skills: Number of available skills
            hidden_dims: Hidden layer dimensions
            activation: Activation function
            output_activation: Output activation function
        """
        super().__init__()
        
        self.obs_dim = obs_dim
        self.num_skills = num_skills
        
        # Build network layers
        layers = []
        input_dim = obs_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(input_dim, hidden_dim),
                self._get_activation(activation)
            ])
            input_dim = hidden_dim
        
        # Output layer
        layers.append(nn.Linear(input_dim, num_skills))
        if output_activation != "linear":
            layers.append(self._get_activation(output_activation))
        
        self.network = nn.Sequential(*layers)
        
        # Initialize weights
        self._init_weights()
    
    def forward(self, obs: torch.Tensor) -> torch.Tensor:
        """
        Forward pass to get skill logits.
        
        Args:
            obs: Observation tensor [batch_size, obs_dim]
            
        Returns:
            Skill logits [batch_size, num_skills]
        """
        return self.network(obs)
    
    def sample_skill(self, obs: torch.Tensor, deterministic: bool = False) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Sample skill from policy.
        
        Args:
            obs: Observation tensor
            deterministic: Whether to use deterministic selection
            
        Returns:
            Tuple of (skill_index, log_prob)
        """
        logits = self.forward(obs)
        
        if deterministic:
            skill = torch.argmax(logits, dim=-1)
            log_prob = F.log_softmax(logits, dim=-1).gather(-1, skill.unsqueeze(-1))
        else:
            dist = torch.distributions.Categorical(logits=logits)
            skill = dist.sample()
            log_prob = dist.log_prob(skill)
        
        return skill, log_prob
    
    def _get_activation(self, activation: str) -> nn.Module:
        """Get activation function by name."""
        activations = {
            "relu": nn.ReLU(),
            "tanh": nn.Tanh(),
            "sigmoid": nn.Sigmoid(),
            "leaky_relu": nn.LeakyReLU(),
            "elu": nn.ELU()
        }
        return activations.get(activation, nn.ReLU())
    
    def _init_weights(self) -> None:
        """Initialize network weights."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.orthogonal_(module.weight, gain=np.sqrt(2))
                nn.init.constant_(module.bias, 0.0)


class LowLevelPolicy(nn.Module):
    """Low-level policy for primitive action execution."""
    
    def __init__(
        self,
        obs_dim: int,
        action_dim: int,
        num_skills: int,
        hidden_dims: list = [64, 64],
        activation: str = "relu",
        output_activation: str = "tanh",
        skill_embedding_dim: int = 16
    ):
        """
        Initialize low-level policy.
        
        Args:
            obs_dim: Observation dimension
            action_dim: Action dimension
            num_skills: Number of skills
            hidden_dims: Hidden layer dimensions
            activation: Activation function
            output_activation: Output activation function
            skill_embedding_dim: Skill embedding dimension
        """
        super().__init__()
        
        self.obs_dim = obs_dim
        self.action_dim = action_dim
        self.num_skills = num_skills
        self.skill_embedding_dim = skill_embedding_dim
        
        # Skill embedding
        self.skill_embedding = nn.Embedding(num_skills, skill_embedding_dim)
        
        # Policy network
        input_dim = obs_dim + skill_embedding_dim
        layers = []
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(input_dim, hidden_dim),
                self._get_activation(activation)
            ])
            input_dim = hidden_dim
        
        # Output layers for mean and log_std
        self.mean_layer = nn.Linear(input_dim, action_dim)
        self.log_std_layer = nn.Linear(input_dim, action_dim)
        
        self.policy_network = nn.Sequential(*layers)
        self.output_activation = self._get_activation(output_activation)
        
        # Initialize weights
        self._init_weights()
    
    def forward(
        self, 
        obs: torch.Tensor, 
        skill: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass to get action distribution parameters.
        
        Args:
            obs: Observation tensor [batch_size, obs_dim]
            skill: Skill index tensor [batch_size]
            
        Returns:
            Tuple of (action_mean, action_log_std)
        """
        # Get skill embedding
        skill_embed = self.skill_embedding(skill)  # [batch_size, skill_embedding_dim]
        
        # Concatenate observation and skill embedding
        input_tensor = torch.cat([obs, skill_embed], dim=-1)
        
        # Forward through network
        features = self.policy_network(input_tensor)
        
        # Get action mean and log std
        action_mean = self.output_activation(self.mean_layer(features))
        action_log_std = self.log_std_layer(features)
        
        # Clamp log std for numerical stability
        action_log_std = torch.clamp(action_log_std, -20, 2)
        
        return action_mean, action_log_std
    
    def sample_action(
        self,
        obs: torch.Tensor,
        skill: torch.Tensor,
        deterministic: bool = False
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Sample action from policy.
        
        Args:
            obs: Observation tensor
            skill: Skill index tensor
            deterministic: Whether to use deterministic action
            
        Returns:
            Tuple of (action, log_prob)
        """
        action_mean, action_log_std = self.forward(obs, skill)
        
        if deterministic:
            action = action_mean
            log_prob = torch.zeros_like(action_mean)
        else:
            action_std = torch.exp(action_log_std)
            dist = torch.distributions.Normal(action_mean, action_std)
            action = dist.sample()
            log_prob = dist.log_prob(action).sum(dim=-1, keepdim=True)
        
        return action, log_prob
    
    def _get_activation(self, activation: str) -> nn.Module:
        """Get activation function by name."""
        activations = {
            "relu": nn.ReLU(),
            "tanh": nn.Tanh(),
            "sigmoid": nn.Sigmoid(),
            "leaky_relu": nn.LeakyReLU(),
            "elu": nn.ELU(),
            "linear": nn.Identity()
        }
        return activations.get(activation, nn.ReLU())
    
    def _init_weights(self) -> None:
        """Initialize network weights."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.orthogonal_(module.weight, gain=np.sqrt(2))
                nn.init.constant_(module.bias, 0.0)
            elif isinstance(module, nn.Embedding):
                nn.init.normal_(module.weight, 0.0, 0.1)


class HierarchicalPolicy(nn.Module):
    """Combined hierarchical policy wrapper."""
    
    def __init__(
        self,
        obs_dim: int,
        action_dim: int,
        num_skills: int,
        config: Dict[str, Any],
        hierarchical: bool = True
    ):
        """
        Initialize hierarchical policy.
        
        Args:
            obs_dim: Observation dimension
            action_dim: Action dimension
            num_skills: Number of skills
            config: Configuration dictionary
            hierarchical: Whether to use hierarchical structure
        """
        super().__init__()
        
        self.obs_dim = obs_dim
        self.action_dim = action_dim
        self.num_skills = num_skills
        self.hierarchical = hierarchical
        
        if hierarchical:
            # High-level policy for skill selection
            high_config = config.get("high_level_policy", {})
            self.high_level_policy = HighLevelPolicy(
                obs_dim=obs_dim,
                num_skills=num_skills,
                hidden_dims=high_config.get("hidden_dims", [128, 128]),
                activation=high_config.get("activation", "relu"),
                output_activation=high_config.get("output_activation", "linear")
            )
            
            # Low-level policy for action execution
            low_config = config.get("low_level_policy", {})
            self.low_level_policy = LowLevelPolicy(
                obs_dim=obs_dim,
                action_dim=action_dim,
                num_skills=num_skills,
                hidden_dims=low_config.get("hidden_dims", [64, 64]),
                activation=low_config.get("activation", "relu"),
                output_activation=low_config.get("output_activation", "tanh")
            )
        else:
            # Flat policy without hierarchy
            self.flat_policy = LowLevelPolicy(
                obs_dim=obs_dim,
                action_dim=action_dim,
                num_skills=1,  # Single "skill"
                hidden_dims=config.get("hidden_dims", [128, 64]),
                activation=config.get("activation", "relu"),
                output_activation=config.get("output_activation", "tanh")
            )
    
    def act(
        self,
        obs: torch.Tensor,
        skill: Optional[torch.Tensor] = None,
        deterministic: bool = False
    ) -> Tuple[torch.Tensor, Dict[str, Any]]:
        """
        Get action from policy.
        
        Args:
            obs: Observation tensor
            skill: Skill index (for low-level policy)
            deterministic: Whether to use deterministic action
            
        Returns:
            Tuple of (action, info_dict)
        """
        if self.hierarchical:
            if skill is None:
                raise ValueError("Skill must be provided for hierarchical policy")
            action, log_prob = self.low_level_policy.sample_action(obs, skill, deterministic)
            info = {"action_log_prob": log_prob}
        else:
            # Use flat policy with dummy skill
            dummy_skill = torch.zeros(obs.size(0), dtype=torch.long, device=obs.device)
            action, log_prob = self.flat_policy.sample_action(obs, dummy_skill, deterministic)
            info = {"action_log_prob": log_prob}
        
        return action, info
