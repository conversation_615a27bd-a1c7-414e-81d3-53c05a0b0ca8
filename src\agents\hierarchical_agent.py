"""
Hierarchical agent with attention-based communication.

This module implements the core hierarchical agent that uses:
- High-level policy for skill selection
- Low-level policy for primitive actions
- Attention mechanism for inter-agent communication
"""

from typing import Dict, Any, Tu<PERSON>, Optional, List
import torch
import torch.nn as nn
import numpy as np
from gym import spaces

from .base_agent import BaseAgent
from src.models.hierarchical_policy import HierarchicalPolicy
from src.models.attention_module import AttentionCommModule
from src.models.value_network import ValueNetwork
from src.communication.attention_comm import AttentionCommunication


class HierarchicalAgent(BaseAgent):
    """Hierarchical agent with attention-based communication."""
    
    def __init__(
        self,
        config: Dict[str, Any],
        observation_space: spaces.Space,
        action_space: spaces.Space,
        agent_id: Optional[int] = None
    ):
        """
        Initialize hierarchical agent.
        
        Args:
            config: Agent configuration
            observation_space: Environment observation space
            action_space: Environment action space
            agent_id: Unique agent identifier
        """
        super().__init__(config, observation_space, action_space, agent_id)
        
        # Extract dimensions
        self.obs_dim = observation_space.shape[0]
        self.action_dim = action_space.shape[0] if hasattr(action_space, 'shape') else action_space.n
        self.num_skills = config.get("num_skills", 8)
        self.skill_length = config.get("skill_length", 10)
        
        # Hierarchical structure
        self.hierarchical = config.get("hierarchical", True)
        
        # Current skill state
        self.current_skill = None
        self.skill_steps_remaining = 0
        
        # Initialize networks
        self._build_networks()
        
        # Communication module
        if config.get("attention", {}).get("enabled", True):
            self.communication = AttentionCommunication(
                config["attention"],
                self.obs_dim
            )
        else:
            self.communication = None
            
        # Move to device
        self.to(self.device)
        
    def _build_networks(self) -> None:
        """Build neural networks for the agent."""
        # Hierarchical policy
        self.policy = HierarchicalPolicy(
            obs_dim=self.obs_dim,
            action_dim=self.action_dim,
            num_skills=self.num_skills,
            config=self.config,
            hierarchical=self.hierarchical
        )
        
        # Value networks
        self.value_network = ValueNetwork(
            obs_dim=self.obs_dim,
            config=self.config.get("value_network", {})
        )
        
        if self.hierarchical:
            # Separate value network for high-level policy
            self.high_value_network = ValueNetwork(
                obs_dim=self.obs_dim,
                config=self.config.get("value_network", {})
            )
    
    def act(
        self,
        observation: np.ndarray,
        other_observations: Optional[List[np.ndarray]] = None,
        deterministic: bool = False
    ) -> Tuple[np.ndarray, Dict[str, Any]]:
        """
        Select action using hierarchical policy.
        
        Args:
            observation: Current observation
            other_observations: Other agents' observations for communication
            deterministic: Whether to use deterministic policy
            
        Returns:
            Tuple of (action, info_dict)
        """
        self.step_count += 1
        
        # Convert to tensor
        obs_tensor = torch.FloatTensor(observation).unsqueeze(0).to(self.device)
        
        # Communication step
        communicated_obs = obs_tensor
        comm_info = {}
        
        if self.communication is not None and other_observations is not None:
            other_obs_tensors = [
                torch.FloatTensor(obs).unsqueeze(0).to(self.device)
                for obs in other_observations
            ]
            communicated_obs, comm_info = self.communication(obs_tensor, other_obs_tensors)
        
        # Hierarchical action selection
        if self.hierarchical:
            action, policy_info = self._hierarchical_act(
                communicated_obs, deterministic
            )
        else:
            # Direct policy without hierarchy
            action, policy_info = self.policy.act(
                communicated_obs, deterministic=deterministic
            )
        
        # Combine info
        info = {
            **policy_info,
            **comm_info,
            "step_count": self.step_count
        }
        
        return action.cpu().numpy().squeeze(), info
    
    def _hierarchical_act(
        self,
        observation: torch.Tensor,
        deterministic: bool = False
    ) -> Tuple[torch.Tensor, Dict[str, Any]]:
        """
        Hierarchical action selection.
        
        Args:
            observation: Processed observation tensor
            deterministic: Whether to use deterministic policy
            
        Returns:
            Tuple of (action, info_dict)
        """
        info = {}
        
        # Check if we need to select a new skill
        if self.current_skill is None or self.skill_steps_remaining <= 0:
            # High-level policy selects skill
            skill_logits = self.policy.high_level_policy(observation)
            
            if deterministic:
                self.current_skill = torch.argmax(skill_logits, dim=-1)
            else:
                skill_dist = torch.distributions.Categorical(logits=skill_logits)
                self.current_skill = skill_dist.sample()
            
            self.skill_steps_remaining = self.skill_length
            info["new_skill"] = self.current_skill.item()
            info["skill_logits"] = skill_logits.cpu().numpy()
        
        # Low-level policy executes skill
        action, low_info = self.policy.low_level_policy(
            observation, 
            self.current_skill,
            deterministic=deterministic
        )
        
        self.skill_steps_remaining -= 1
        
        info.update(low_info)
        info["current_skill"] = self.current_skill.item()
        info["skill_steps_remaining"] = self.skill_steps_remaining
        
        return action, info
    
    def update(self, experiences: Dict[str, Any]) -> Dict[str, float]:
        """
        Update agent using collected experiences.
        
        Args:
            experiences: Experience dictionary
            
        Returns:
            Training metrics
        """
        if not self.training:
            return {}
        
        # Convert experiences to tensors
        obs = torch.FloatTensor(experiences["observations"]).to(self.device)
        actions = torch.FloatTensor(experiences["actions"]).to(self.device)
        rewards = torch.FloatTensor(experiences["rewards"]).to(self.device)
        dones = torch.FloatTensor(experiences["dones"]).to(self.device)
        
        # Update policy and value networks
        if self.hierarchical:
            metrics = self._update_hierarchical(obs, actions, rewards, dones, experiences)
        else:
            metrics = self._update_flat(obs, actions, rewards, dones)
        
        return metrics
    
    def _update_hierarchical(
        self,
        obs: torch.Tensor,
        actions: torch.Tensor,
        rewards: torch.Tensor,
        dones: torch.Tensor,
        experiences: Dict[str, Any]
    ) -> Dict[str, float]:
        """Update hierarchical policy."""
        # This is a simplified update - full implementation would use PPO/A2C
        metrics = {}
        
        # Placeholder for hierarchical update logic
        # Would implement proper high-level and low-level policy updates
        
        return metrics
    
    def _update_flat(
        self,
        obs: torch.Tensor,
        actions: torch.Tensor,
        rewards: torch.Tensor,
        dones: torch.Tensor
    ) -> Dict[str, float]:
        """Update flat policy."""
        # Placeholder for flat policy update
        metrics = {}
        return metrics
    
    def reset(self) -> None:
        """Reset agent for new episode."""
        super().reset()
        self.current_skill = None
        self.skill_steps_remaining = 0
        
        if self.communication is not None:
            self.communication.reset()
    
    def save_checkpoint(self, filepath: str) -> None:
        """Save agent checkpoint."""
        checkpoint = {
            "policy_state_dict": self.policy.state_dict(),
            "value_network_state_dict": self.value_network.state_dict(),
            "agent_state_dict": self.state_dict(),
            "config": self.config
        }
        
        if self.hierarchical:
            checkpoint["high_value_network_state_dict"] = self.high_value_network.state_dict()
        
        if self.communication is not None:
            checkpoint["communication_state_dict"] = self.communication.state_dict()
        
        torch.save(checkpoint, filepath)
    
    def load_checkpoint(self, filepath: str) -> None:
        """Load agent checkpoint."""
        checkpoint = torch.load(filepath, map_location=self.device)
        
        self.policy.load_state_dict(checkpoint["policy_state_dict"])
        self.value_network.load_state_dict(checkpoint["value_network_state_dict"])
        self.load_state_dict(checkpoint["agent_state_dict"])
        
        if self.hierarchical and "high_value_network_state_dict" in checkpoint:
            self.high_value_network.load_state_dict(checkpoint["high_value_network_state_dict"])
        
        if self.communication is not None and "communication_state_dict" in checkpoint:
            self.communication.load_state_dict(checkpoint["communication_state_dict"])
    
    def to(self, device: torch.device) -> "HierarchicalAgent":
        """Move agent to device."""
        super().to(device)
        self.policy.to(device)
        self.value_network.to(device)
        
        if self.hierarchical:
            self.high_value_network.to(device)
        
        if self.communication is not None:
            self.communication.to(device)
        
        return self
    
    def parameters(self):
        """Get all trainable parameters."""
        params = list(self.policy.parameters()) + list(self.value_network.parameters())
        
        if self.hierarchical:
            params.extend(self.high_value_network.parameters())
        
        if self.communication is not None:
            params.extend(self.communication.parameters())
        
        return iter(params)
