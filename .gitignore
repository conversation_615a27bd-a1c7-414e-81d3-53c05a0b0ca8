# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# PyTorch
*.pth
*.pt

# TensorBoard logs
runs/
logs/
tensorboard_logs/

# Weights & Biases
wandb/

# Data directories
data/raw/*
data/processed/*
data/checkpoints/*
data/logs/*
!data/raw/.gitkeep
!data/processed/.gitkeep
!data/checkpoints/.gitkeep
!data/logs/.gitkeep

# Results directories
results/figures/*
results/tables/*
results/videos/*
results/reports/*
!results/figures/.gitkeep
!results/tables/.gitkeep
!results/videos/.gitkeep
!results/reports/.gitkeep

# Experiment outputs
experiments/outputs/
experiments/results/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# LaTeX files
*.aux
*.bbl
*.blg
*.fdb_latexmk
*.fls
*.log
*.out
*.synctex.gz
*.toc
*.nav
*.snm
*.vrb

# Paper drafts
paper/drafts/
paper/*.pdf
!paper/main.pdf

# Profiling
*.prof
*.profile

# Large model files
*.h5
*.hdf5
*.pkl
*.pickle

# Configuration overrides
configs/local/
configs/personal/

# Slurm job files
*.out
*.err
slurm-*.out

# Ray Tune results
ray_results/

# Hydra outputs
outputs/
.hydra/

# MLflow
mlruns/

# DVC
.dvc/
*.dvc

# Optuna
optuna_studies/

# Custom experiment directories
experiments_*/
results_*/
logs_*/
