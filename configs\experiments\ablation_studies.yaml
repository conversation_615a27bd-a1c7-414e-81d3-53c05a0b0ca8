# Ablation study configuration
# Tests the contribution of different components

# Inherit from base config
defaults:
  - ../config

# Override experiment settings
experiment:
  name: "ablation_studies"
  description: "Ablation study of key components"
  
# Ablation configurations
ablations:
  full_model:
    description: "Full model with all components"
    agent:
      hierarchical: true
      attention:
        enabled: true
      communication:
        type: "attention"
        
  no_attention:
    description: "Remove attention mechanism"
    agent:
      hierarchical: true
      attention:
        enabled: false
      communication:
        type: "mean_pool"
        
  no_hierarchy:
    description: "Remove hierarchical structure"
    agent:
      hierarchical: false
      attention:
        enabled: true
      communication:
        type: "attention"
        
  no_communication:
    description: "Remove inter-agent communication"
    agent:
      hierarchical: true
      attention:
        enabled: false
      communication:
        type: "none"
        
  attention_only:
    description: "Only attention, no hierarchy"
    agent:
      hierarchical: false
      attention:
        enabled: true
      communication:
        type: "attention"
        
  hierarchy_only:
    description: "Only hierarchy, no communication"
    agent:
      hierarchical: true
      attention:
        enabled: false
      communication:
        type: "none"
        
  minimal_baseline:
    description: "No hierarchy, no attention, no communication"
    agent:
      hierarchical: false
      attention:
        enabled: false
      communication:
        type: "none"

# Training settings for ablations
training:
  total_timesteps: 750000
  eval_freq: 25000
  
# Environment for ablations
environment:
  name: "mpe_spread"
  num_agents: 8
  max_episode_steps: 100
  
# Seeds for statistical significance
seeds: [0, 1, 2, 3, 4]

# Metrics for ablation analysis
logging:
  metrics:
    - "episode_reward"
    - "episode_length"
    - "coordination_score"
    - "skill_usage"
    - "attention_entropy"
    - "communication_efficiency"
    - "convergence_speed"
    - "final_performance"
    
# Analysis settings
analysis:
  statistical_tests: true
  significance_level: 0.05
  
  plots:
    - "ablation_comparison"
    - "component_contribution"
    - "learning_curves_comparison"
    - "performance_heatmap"
