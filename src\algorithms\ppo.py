"""
Proximal Policy Optimization (PPO) algorithm implementation.

This module implements PPO for hierarchical multi-agent reinforcement learning
with support for attention-based communication.
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from typing import Dict, Any, Tuple, Optional


class PPOAlgorithm:
    """PPO algorithm for hierarchical multi-agent RL."""
    
    def __init__(
        self,
        agent,
        config: Dict[str, Any],
        device: torch.device
    ):
        """
        Initialize PPO algorithm.
        
        Args:
            agent: Agent to train
            config: Training configuration
            device: Device for computations
        """
        self.agent = agent
        self.device = device
        
        # PPO hyperparameters
        self.learning_rate = config.get("learning_rate", 3e-4)
        self.clip_range = config.get("clip_range", 0.2)
        self.entropy_coef = config.get("entropy_coef", 0.01)
        self.value_loss_coef = config.get("value_loss_coef", 0.5)
        self.max_grad_norm = config.get("max_grad_norm", 0.5)
        self.num_epochs = config.get("num_epochs", 10)
        self.batch_size = config.get("batch_size", 256)
        
        # Hierarchical learning rates
        self.high_level_lr = config.get("high_level_lr", self.learning_rate)
        self.low_level_lr = config.get("low_level_lr", self.learning_rate)
        
        # Initialize optimizers
        self._setup_optimizers()
        
        # Training statistics
        self.training_stats = {
            "policy_loss": [],
            "value_loss": [],
            "entropy_loss": [],
            "total_loss": [],
            "grad_norm": []
        }
    
    def _setup_optimizers(self) -> None:
        """Setup optimizers for different components."""
        if self.agent.hierarchical:
            # Separate optimizers for high and low level policies
            high_level_params = list(self.agent.policy.high_level_policy.parameters())
            low_level_params = list(self.agent.policy.low_level_policy.parameters())
            value_params = list(self.agent.value_network.parameters())
            
            self.high_optimizer = optim.Adam(high_level_params, lr=self.high_level_lr)
            self.low_optimizer = optim.Adam(low_level_params, lr=self.low_level_lr)
            self.value_optimizer = optim.Adam(value_params, lr=self.learning_rate)
            
            if hasattr(self.agent, 'high_value_network'):
                high_value_params = list(self.agent.high_value_network.parameters())
                self.high_value_optimizer = optim.Adam(high_value_params, lr=self.learning_rate)
        else:
            # Single optimizer for flat policy
            all_params = list(self.agent.parameters())
            self.optimizer = optim.Adam(all_params, lr=self.learning_rate)
    
    def update(self, training_data: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """
        Update agent using PPO algorithm.
        
        Args:
            training_data: Training data dictionary
            
        Returns:
            Training metrics
        """
        observations = training_data["observations"]
        actions = training_data["actions"]
        rewards = training_data["rewards"]
        dones = training_data["dones"]
        
        # Compute advantages and returns
        advantages, returns = self._compute_advantages(observations, rewards, dones)
        
        # Get old policy log probabilities and values
        with torch.no_grad():
            old_log_probs, old_values = self._evaluate_actions(observations, actions)
        
        # PPO training loop
        total_policy_loss = 0.0
        total_value_loss = 0.0
        total_entropy_loss = 0.0
        
        for epoch in range(self.num_epochs):
            # Create mini-batches
            indices = torch.randperm(len(observations))
            
            for start_idx in range(0, len(observations), self.batch_size):
                end_idx = min(start_idx + self.batch_size, len(observations))
                batch_indices = indices[start_idx:end_idx]
                
                # Get batch data
                batch_obs = observations[batch_indices]
                batch_actions = actions[batch_indices]
                batch_advantages = advantages[batch_indices]
                batch_returns = returns[batch_indices]
                batch_old_log_probs = old_log_probs[batch_indices]
                batch_old_values = old_values[batch_indices]
                
                # Compute losses
                policy_loss, value_loss, entropy_loss = self._compute_losses(
                    batch_obs, batch_actions, batch_advantages, batch_returns,
                    batch_old_log_probs, batch_old_values
                )
                
                # Update networks
                self._update_networks(policy_loss, value_loss, entropy_loss)
                
                # Accumulate losses
                total_policy_loss += policy_loss.item()
                total_value_loss += value_loss.item()
                total_entropy_loss += entropy_loss.item()
        
        # Compute average losses
        num_updates = self.num_epochs * (len(observations) // self.batch_size + 1)
        avg_policy_loss = total_policy_loss / num_updates
        avg_value_loss = total_value_loss / num_updates
        avg_entropy_loss = total_entropy_loss / num_updates
        
        # Update training statistics
        self.training_stats["policy_loss"].append(avg_policy_loss)
        self.training_stats["value_loss"].append(avg_value_loss)
        self.training_stats["entropy_loss"].append(avg_entropy_loss)
        self.training_stats["total_loss"].append(avg_policy_loss + avg_value_loss + avg_entropy_loss)
        
        return {
            "policy_loss": avg_policy_loss,
            "value_loss": avg_value_loss,
            "entropy_loss": avg_entropy_loss,
            "total_loss": avg_policy_loss + avg_value_loss + avg_entropy_loss
        }
    
    def _compute_advantages(
        self,
        observations: torch.Tensor,
        rewards: torch.Tensor,
        dones: torch.Tensor,
        gamma: float = 0.99,
        gae_lambda: float = 0.95
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Compute advantages using Generalized Advantage Estimation (GAE).
        
        Args:
            observations: Observations tensor
            rewards: Rewards tensor
            dones: Done flags tensor
            gamma: Discount factor
            gae_lambda: GAE lambda parameter
            
        Returns:
            Tuple of (advantages, returns)
        """
        with torch.no_grad():
            # Get value estimates
            values = self.agent.value_network(observations).squeeze(-1)
            
            # Compute advantages using GAE
            advantages = torch.zeros_like(rewards)
            returns = torch.zeros_like(rewards)
            
            gae = 0
            for t in reversed(range(len(rewards))):
                if t == len(rewards) - 1:
                    next_value = 0
                else:
                    next_value = values[t + 1]
                
                delta = rewards[t] + gamma * next_value * (1 - dones[t]) - values[t]
                gae = delta + gamma * gae_lambda * (1 - dones[t]) * gae
                advantages[t] = gae
                returns[t] = advantages[t] + values[t]
            
            # Normalize advantages
            advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        return advantages, returns
    
    def _evaluate_actions(
        self,
        observations: torch.Tensor,
        actions: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Evaluate actions to get log probabilities and values.
        
        Args:
            observations: Observations tensor
            actions: Actions tensor
            
        Returns:
            Tuple of (log_probs, values)
        """
        # This is a simplified implementation
        # In practice, would need to handle hierarchical structure properly
        
        # Get values
        values = self.agent.value_network(observations).squeeze(-1)
        
        # For now, return dummy log probs
        # Real implementation would compute actual log probabilities
        log_probs = torch.zeros(len(observations), device=self.device)
        
        return log_probs, values
    
    def _compute_losses(
        self,
        observations: torch.Tensor,
        actions: torch.Tensor,
        advantages: torch.Tensor,
        returns: torch.Tensor,
        old_log_probs: torch.Tensor,
        old_values: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Compute PPO losses.
        
        Args:
            observations: Batch observations
            actions: Batch actions
            advantages: Batch advantages
            returns: Batch returns
            old_log_probs: Old policy log probabilities
            old_values: Old value estimates
            
        Returns:
            Tuple of (policy_loss, value_loss, entropy_loss)
        """
        # Get current policy log probs and values
        current_log_probs, current_values = self._evaluate_actions(observations, actions)
        
        # Policy loss (PPO clipped objective)
        ratio = torch.exp(current_log_probs - old_log_probs)
        surr1 = ratio * advantages
        surr2 = torch.clamp(ratio, 1 - self.clip_range, 1 + self.clip_range) * advantages
        policy_loss = -torch.min(surr1, surr2).mean()
        
        # Value loss (clipped)
        value_pred_clipped = old_values + torch.clamp(
            current_values - old_values, -self.clip_range, self.clip_range
        )
        value_loss1 = (current_values - returns).pow(2)
        value_loss2 = (value_pred_clipped - returns).pow(2)
        value_loss = 0.5 * torch.max(value_loss1, value_loss2).mean()
        
        # Entropy loss (for exploration)
        # This is a placeholder - real implementation would compute actual entropy
        entropy_loss = torch.tensor(0.0, device=self.device)
        
        return policy_loss, value_loss, entropy_loss
    
    def _update_networks(
        self,
        policy_loss: torch.Tensor,
        value_loss: torch.Tensor,
        entropy_loss: torch.Tensor
    ) -> None:
        """
        Update networks using computed losses.
        
        Args:
            policy_loss: Policy loss
            value_loss: Value loss
            entropy_loss: Entropy loss
        """
        if self.agent.hierarchical:
            # Update high-level policy
            self.high_optimizer.zero_grad()
            policy_loss.backward(retain_graph=True)
            torch.nn.utils.clip_grad_norm_(
                self.agent.policy.high_level_policy.parameters(),
                self.max_grad_norm
            )
            self.high_optimizer.step()
            
            # Update low-level policy
            self.low_optimizer.zero_grad()
            policy_loss.backward(retain_graph=True)
            torch.nn.utils.clip_grad_norm_(
                self.agent.policy.low_level_policy.parameters(),
                self.max_grad_norm
            )
            self.low_optimizer.step()
            
            # Update value network
            self.value_optimizer.zero_grad()
            value_loss.backward()
            torch.nn.utils.clip_grad_norm_(
                self.agent.value_network.parameters(),
                self.max_grad_norm
            )
            self.value_optimizer.step()
        else:
            # Update all parameters together
            total_loss = policy_loss + self.value_loss_coef * value_loss + self.entropy_coef * entropy_loss
            
            self.optimizer.zero_grad()
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(
                self.agent.parameters(),
                self.max_grad_norm
            )
            self.optimizer.step()
    
    def get_training_stats(self) -> Dict[str, Any]:
        """Get training statistics."""
        return {
            "policy_loss_history": self.training_stats["policy_loss"].copy(),
            "value_loss_history": self.training_stats["value_loss"].copy(),
            "entropy_loss_history": self.training_stats["entropy_loss"].copy(),
            "total_loss_history": self.training_stats["total_loss"].copy()
        }
