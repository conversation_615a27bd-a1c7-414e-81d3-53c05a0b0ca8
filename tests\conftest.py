"""
Pytest configuration and shared fixtures.

This file contains shared fixtures and configuration for all tests.
"""

import pytest
import torch
import numpy as np
import tempfile
import shutil
from pathlib import Path
from gym import spaces

from src.utils.seed_manager import set_global_seed


@pytest.fixture(autouse=True)
def setup_test_environment():
    """Setup test environment before each test."""
    # Set deterministic behavior
    set_global_seed(42)
    
    # Set PyTorch to deterministic mode for testing
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    
    yield
    
    # Cleanup after test
    torch.cuda.empty_cache() if torch.cuda.is_available() else None


@pytest.fixture
def temp_directory():
    """Create a temporary directory for test files."""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)


@pytest.fixture
def sample_observation_space():
    """Standard observation space for testing."""
    return spaces.Box(low=-np.inf, high=np.inf, shape=(8,), dtype=np.float32)


@pytest.fixture
def sample_action_space():
    """Standard action space for testing."""
    return spaces.Box(low=-1.0, high=1.0, shape=(2,), dtype=np.float32)


@pytest.fixture
def sample_discrete_action_space():
    """Discrete action space for testing."""
    return spaces.Discrete(4)


@pytest.fixture
def minimal_agent_config():
    """Minimal agent configuration for testing."""
    return {
        "hierarchical": True,
        "num_skills": 3,
        "skill_length": 5,
        "attention": {
            "enabled": True,
            "embed_dim": 16,
            "num_heads": 2,
            "dropout": 0.1,
            "max_agents": 8
        },
        "high_level_policy": {
            "hidden_dims": [32, 16],
            "activation": "relu"
        },
        "low_level_policy": {
            "hidden_dims": [16, 8],
            "activation": "relu"
        },
        "value_network": {
            "hidden_dims": [32, 16],
            "activation": "relu"
        }
    }


@pytest.fixture
def minimal_env_config():
    """Minimal environment configuration for testing."""
    return {
        "name": "moving_goals",
        "num_agents": 3,
        "max_episode_steps": 20
    }


@pytest.fixture
def minimal_training_config():
    """Minimal training configuration for testing."""
    return {
        "algorithm": "ppo",
        "total_timesteps": 100,
        "learning_rate": 1e-3,
        "batch_size": 16,
        "num_epochs": 2,
        "eval_freq": 50,
        "eval_episodes": 3
    }


@pytest.fixture
def full_minimal_config(temp_directory, minimal_agent_config, minimal_env_config, minimal_training_config):
    """Complete minimal configuration for testing."""
    return {
        "experiment": {
            "name": "test_experiment",
            "seed": 42,
            "save_dir": str(Path(temp_directory) / "checkpoints"),
            "log_dir": str(Path(temp_directory) / "logs"),
            "device": "cpu"
        },
        "environment": minimal_env_config,
        "agent": minimal_agent_config,
        "training": minimal_training_config,
        "logging": {
            "level": "INFO",
            "log_interval": 10,
            "tensorboard": False,
            "wandb": False,
            "metrics": ["episode_reward", "episode_length"]
        },
        "checkpoint": {
            "save_best": True,
            "save_last": True,
            "save_interval": 50,
            "max_checkpoints": 3
        }
    }


@pytest.fixture
def sample_batch_data():
    """Sample batch data for testing algorithms."""
    batch_size = 16
    obs_dim = 8
    action_dim = 2
    
    return {
        "observations": torch.randn(batch_size, obs_dim),
        "actions": torch.randn(batch_size, action_dim),
        "rewards": torch.randn(batch_size),
        "dones": torch.zeros(batch_size),
        "advantages": torch.randn(batch_size),
        "returns": torch.randn(batch_size),
        "log_probs": torch.randn(batch_size),
        "values": torch.randn(batch_size)
    }


@pytest.fixture
def mock_episode_data():
    """Mock episode data for testing."""
    episode_length = 10
    num_agents = 3
    obs_dim = 8
    action_dim = 2
    
    return {
        "observations": [
            [np.random.randn(obs_dim) for _ in range(num_agents)]
            for _ in range(episode_length)
        ],
        "actions": [
            [np.random.randn(action_dim) for _ in range(num_agents)]
            for _ in range(episode_length)
        ],
        "rewards": [
            [np.random.randn() for _ in range(num_agents)]
            for _ in range(episode_length)
        ],
        "dones": [
            [False for _ in range(num_agents)]
            for _ in range(episode_length)
        ],
        "infos": [
            [{"step": step, "agent": agent} for agent in range(num_agents)]
            for step in range(episode_length)
        ],
        "episode_reward": np.random.randn(),
        "episode_length": episode_length
    }


# Pytest markers for test categorization
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line("markers", "unit: Unit tests for individual components")
    config.addinivalue_line("markers", "integration: Integration tests for component interactions")
    config.addinivalue_line("markers", "slow: Tests that take a long time to run")
    config.addinivalue_line("markers", "gpu: Tests that require GPU")
    config.addinivalue_line("markers", "mpe: Tests that require MPE environments")


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers automatically."""
    for item in items:
        # Add unit marker to unit test files
        if "test_" in item.fspath.basename and "integration" not in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        
        # Add integration marker to integration test files
        if "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        
        # Add slow marker to tests that might be slow
        if any(keyword in item.name.lower() for keyword in ["training", "pipeline", "full"]):
            item.add_marker(pytest.mark.slow)


# Skip tests that require optional dependencies
def pytest_runtest_setup(item):
    """Setup function called before each test."""
    # Skip MPE tests if PettingZoo is not available
    if item.get_closest_marker("mpe"):
        try:
            import pettingzoo
        except ImportError:
            pytest.skip("PettingZoo not available")
    
    # Skip GPU tests if CUDA is not available
    if item.get_closest_marker("gpu"):
        if not torch.cuda.is_available():
            pytest.skip("CUDA not available")


# Custom assertions for testing
class CustomAssertions:
    """Custom assertion helpers for testing."""
    
    @staticmethod
    def assert_tensor_shape(tensor, expected_shape):
        """Assert tensor has expected shape."""
        assert tensor.shape == expected_shape, f"Expected shape {expected_shape}, got {tensor.shape}"
    
    @staticmethod
    def assert_tensor_range(tensor, min_val, max_val):
        """Assert tensor values are in expected range."""
        assert tensor.min() >= min_val, f"Tensor minimum {tensor.min()} below {min_val}"
        assert tensor.max() <= max_val, f"Tensor maximum {tensor.max()} above {max_val}"
    
    @staticmethod
    def assert_dict_keys(dictionary, expected_keys):
        """Assert dictionary has expected keys."""
        missing_keys = set(expected_keys) - set(dictionary.keys())
        assert not missing_keys, f"Missing keys: {missing_keys}"
    
    @staticmethod
    def assert_episode_data_valid(episode_data):
        """Assert episode data has valid structure."""
        required_keys = ["observations", "actions", "rewards", "dones", "episode_reward", "episode_length"]
        CustomAssertions.assert_dict_keys(episode_data, required_keys)
        
        episode_length = episode_data["episode_length"]
        assert len(episode_data["observations"]) == episode_length
        assert len(episode_data["actions"]) == episode_length
        assert len(episode_data["rewards"]) == episode_length
        assert len(episode_data["dones"]) == episode_length


@pytest.fixture
def custom_assertions():
    """Provide custom assertion helpers."""
    return CustomAssertions()
