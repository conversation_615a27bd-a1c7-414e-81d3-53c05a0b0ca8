# Getting Started with Scalable Decentralized HRL

This guide will help you get started with the Scalable Decentralized Hierarchical Reinforcement Learning framework.

## Installation

### Prerequisites

- Python 3.8 or higher
- PyTorch 1.9 or higher
- CUDA (optional, for GPU acceleration)

### Step 1: Clone the Repository

```bash
git clone <repository-url>
cd hrl_attention_marl
```

### Step 2: Create Virtual Environment

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### Step 3: Install Dependencies

```bash
pip install -r requirements.txt
pip install -e .
```

### Step 4: Verify Installation

```bash
python -c "import src; print('Installation successful!')"
```

## Quick Start

### Basic Training

Run a basic training session with default settings:

```bash
python -m src.main --config configs/config.yaml --mode train
```

### Custom Configuration

Create a custom configuration or modify existing ones:

```bash
# Copy default config
cp configs/config.yaml configs/my_config.yaml

# Edit the configuration
# Then run with your config
python -m src.main --config configs/my_config.yaml --mode train
```

### Evaluation

Evaluate a trained model:

```bash
python -m src.main --config configs/config.yaml --mode eval --checkpoint data/checkpoints/best_model.pt
```

## Configuration

The framework uses YAML configuration files for easy experimentation. Key configuration sections:

### Environment Configuration

```yaml
environment:
  name: "mpe_spread"  # Environment type
  num_agents: 5       # Number of agents
  max_episode_steps: 100
```

### Agent Configuration

```yaml
agent:
  hierarchical: true    # Enable hierarchical structure
  num_skills: 8        # Number of high-level skills
  skill_length: 10     # Steps per skill execution
  
  attention:
    enabled: true      # Enable attention communication
    embed_dim: 64      # Attention embedding dimension
    num_heads: 4       # Number of attention heads
```

### Training Configuration

```yaml
training:
  algorithm: "ppo"           # RL algorithm
  total_timesteps: 1000000   # Total training steps
  learning_rate: 3e-4        # Learning rate
  batch_size: 256            # Batch size
```

## Running Experiments

### Scalability Experiment

Test how the method scales with different numbers of agents:

```bash
python -m experiments.run_scalability --agent-counts 2,5,10,20 --num-seeds 5
```

### Ablation Studies

Run ablation studies to understand component contributions:

```bash
python -m experiments.run_ablations --components attention,hierarchy,communication
```

### Custom Experiments

Create your own experiment scripts following the pattern in `experiments/`:

```python
from src.main import load_config, setup_device
from src.trainers.hierarchical_trainer import HierarchicalTrainer
from src.environments.environment_loader import load_environment
from src.agents.hierarchical_agent import HierarchicalAgent

# Load configuration
config = load_config("configs/my_experiment.yaml", args)

# Setup components
device = setup_device(config)
env = load_environment(config["environment"])
agent = HierarchicalAgent(config["agent"], env.observation_space, env.action_space)

# Create trainer and run
trainer = HierarchicalTrainer(agent, env, config, device)
trainer.train()
```

## Analysis and Visualization

### Generate Plots

Create learning curves and performance plots:

```bash
python -m analysis.plot_results --results-dir results/my_experiment --plot-type all
```

### Custom Analysis

Use the analysis tools for custom visualizations:

```python
from analysis.plot_results import plot_learning_curves, plot_scalability_results

# Plot learning curves
plot_learning_curves("results/experiment1", metrics=["episode_reward", "coordination_score"])

# Plot scalability results
plot_scalability_results("results/scalability_sweep/scalability_summary.json")
```

## Environments

### Supported Environments

1. **MPE Spread**: Agents must spread out to cover landmarks
2. **MPE Tag**: Adversarial agents try to catch good agents
3. **Moving Goals**: Dynamic target environments
4. **Custom Swarm**: Configurable swarm robotics scenarios

### Adding New Environments

1. Create environment class in `src/environments/`
2. Add loader function in `environment_loader.py`
3. Add configuration in `configs/environments/`

Example:

```python
class MyCustomEnv:
    def __init__(self, config):
        self.config = config
        # Initialize environment
    
    def reset(self):
        # Return initial observations
        pass
    
    def step(self, actions):
        # Return obs, rewards, dones, info
        pass
```

## Monitoring and Logging

### TensorBoard

Enable TensorBoard logging:

```yaml
logging:
  tensorboard: true
```

Then view logs:

```bash
tensorboard --logdir data/logs
```

### Weights & Biases

Enable W&B logging:

```yaml
logging:
  wandb: true
```

### Custom Metrics

Add custom metrics to track:

```yaml
logging:
  metrics:
    - "episode_reward"
    - "coordination_score"
    - "my_custom_metric"
```

## Troubleshooting

### Common Issues

1. **CUDA out of memory**: Reduce batch size or number of agents
2. **Slow training**: Enable GPU acceleration or reduce environment complexity
3. **Poor convergence**: Adjust learning rates or network architectures

### Debug Mode

Enable debug mode for detailed logging:

```bash
python -m src.main --config configs/config.yaml --debug
```

### Performance Profiling

Profile training performance:

```bash
python -m src.main --config configs/config.yaml --profile
```

## Advanced Usage

### Distributed Training

Enable distributed training across multiple GPUs:

```yaml
distributed:
  enabled: true
  num_workers: 4
```

### Hyperparameter Optimization

Use automated hyperparameter search:

```yaml
optimization:
  enabled: true
  method: "optuna"
  num_trials: 100
```

### Custom Models

Implement custom neural network architectures:

```python
from src.models.base_model import BaseModel

class MyCustomModel(BaseModel):
    def __init__(self, config):
        super().__init__(config)
        # Define custom architecture
    
    def forward(self, x):
        # Implement forward pass
        pass
```

## Next Steps

1. **Experiment with different configurations** to understand the framework
2. **Run scalability experiments** to see how performance changes with agent count
3. **Implement custom environments** for your specific use case
4. **Analyze attention patterns** to understand communication behavior
5. **Compare with baseline methods** using the provided implementations

For more detailed information, see the full documentation in the `docs/` directory.

## Getting Help

- Check the [FAQ](docs/faq.md) for common questions
- Review [API documentation](docs/api/) for detailed function references
- Look at [example scripts](examples/) for usage patterns
- Open an issue on GitHub for bugs or feature requests

Happy experimenting with hierarchical multi-agent reinforcement learning!
