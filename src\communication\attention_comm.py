"""
Attention-based communication for multi-agent coordination.

This module implements the core attention mechanism that enables
agents to selectively communicate and share information.
"""

import torch
import torch.nn as nn
from typing import List, Tuple, Dict, Any, Optional
import math

from src.models.attention_module import AttentionCommModule


class AttentionCommunication(nn.Module):
    """Attention-based communication system for multi-agent coordination."""
    
    def __init__(
        self,
        config: Dict[str, Any],
        obs_dim: int
    ):
        """
        Initialize attention communication.
        
        Args:
            config: Communication configuration
            obs_dim: Observation dimension
        """
        super().__init__()
        
        self.obs_dim = obs_dim
        self.embed_dim = config.get("embed_dim", 64)
        self.num_heads = config.get("num_heads", 4)
        self.dropout = config.get("dropout", 0.1)
        self.max_agents = config.get("max_agents", 20)
        self.message_dim = config.get("message_dim", 32)
        
        # Observation encoder
        self.obs_encoder = nn.Sequential(
            nn.Linear(obs_dim, self.embed_dim),
            nn.ReLU(),
            nn.Linear(self.embed_dim, self.embed_dim)
        )
        
        # Attention module
        self.attention_module = AttentionCommModule(
            embed_dim=self.embed_dim,
            num_heads=self.num_heads,
            dropout=self.dropout,
            max_agents=self.max_agents
        )
        
        # Message decoder
        self.message_decoder = nn.Sequential(
            nn.Linear(self.embed_dim, self.message_dim),
            nn.ReLU(),
            nn.Linear(self.message_dim, obs_dim)
        )
        
        # Communication range (if specified)
        self.range_limit = config.get("range_limit", None)
        
        # Communication history for analysis
        self.communication_history = []
        
    def forward(
        self,
        own_obs: torch.Tensor,
        other_obs: List[torch.Tensor],
        positions: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, Dict[str, Any]]:
        """
        Perform attention-based communication.
        
        Args:
            own_obs: Own observation [batch_size, obs_dim]
            other_obs: List of other agents' observations
            positions: Agent positions for range-based communication
            
        Returns:
            Tuple of (communicated_observation, info_dict)
        """
        batch_size = own_obs.size(0)
        num_other_agents = len(other_obs)
        
        if num_other_agents == 0:
            # No other agents to communicate with
            return own_obs, {"attention_weights": None, "num_communicating_agents": 0}
        
        # Encode observations
        own_encoded = self.obs_encoder(own_obs)  # [batch_size, embed_dim]
        
        # Stack other observations and encode
        other_obs_stacked = torch.stack(other_obs, dim=1)  # [batch_size, num_agents, obs_dim]
        other_encoded = self.obs_encoder(other_obs_stacked.view(-1, self.obs_dim))
        other_encoded = other_encoded.view(batch_size, num_other_agents, self.embed_dim)
        
        # Apply range-based filtering if specified
        attention_mask = None
        if self.range_limit is not None and positions is not None:
            attention_mask = self._compute_range_mask(positions, self.range_limit)
        
        # Apply attention communication
        communicated_encoded, attention_info = self.attention_module(
            query_obs=own_encoded,
            key_value_obs=other_encoded,
            attention_mask=attention_mask,
            return_attention=True
        )
        
        # Decode back to observation space
        communicated_obs = self.message_decoder(communicated_encoded)
        
        # Combine with original observation
        combined_obs = own_obs + communicated_obs
        
        # Prepare info dictionary
        info = {
            **attention_info,
            "num_communicating_agents": num_other_agents,
            "communication_range": self.range_limit,
            "message_norm": torch.norm(communicated_obs, dim=-1).mean().item()
        }
        
        # Store communication history
        if self.training:
            self.communication_history.append({
                "attention_weights": attention_info.get("attention_weights"),
                "message_norm": info["message_norm"],
                "num_agents": num_other_agents + 1
            })
        
        return combined_obs, info
    
    def _compute_range_mask(
        self,
        positions: torch.Tensor,
        range_limit: float
    ) -> torch.Tensor:
        """
        Compute attention mask based on communication range.
        
        Args:
            positions: Agent positions [batch_size, num_agents, 2]
            range_limit: Maximum communication range
            
        Returns:
            Attention mask [batch_size, 1, num_agents]
        """
        batch_size, num_agents, _ = positions.size()
        
        # Compute pairwise distances
        own_pos = positions[:, 0:1, :]  # [batch_size, 1, 2]
        other_pos = positions[:, 1:, :]  # [batch_size, num_agents-1, 2]
        
        distances = torch.norm(own_pos - other_pos, dim=-1)  # [batch_size, num_agents-1]
        
        # Create mask (True where communication is allowed)
        mask = distances <= range_limit  # [batch_size, num_agents-1]
        
        # Add batch dimension for attention
        mask = mask.unsqueeze(1)  # [batch_size, 1, num_agents-1]
        
        return mask
    
    def get_communication_stats(self) -> Dict[str, Any]:
        """
        Get communication statistics from history.
        
        Returns:
            Dictionary of communication statistics
        """
        if not self.communication_history:
            return {}
        
        # Compute statistics
        message_norms = [entry["message_norm"] for entry in self.communication_history]
        num_agents_list = [entry["num_agents"] for entry in self.communication_history]
        
        stats = {
            "avg_message_norm": sum(message_norms) / len(message_norms),
            "max_message_norm": max(message_norms),
            "min_message_norm": min(message_norms),
            "avg_num_agents": sum(num_agents_list) / len(num_agents_list),
            "total_communications": len(self.communication_history)
        }
        
        return stats
    
    def reset(self) -> None:
        """Reset communication state."""
        self.communication_history.clear()
    
    def set_range_limit(self, range_limit: Optional[float]) -> None:
        """
        Set communication range limit.
        
        Args:
            range_limit: New range limit (None for unlimited)
        """
        self.range_limit = range_limit
    
    def get_attention_patterns(self) -> Optional[torch.Tensor]:
        """
        Get recent attention patterns for analysis.
        
        Returns:
            Attention weights tensor or None
        """
        if not self.communication_history:
            return None
        
        # Get most recent attention weights
        recent_attention = self.communication_history[-1]["attention_weights"]
        return recent_attention
    
    def enable_communication_logging(self, enable: bool = True) -> None:
        """
        Enable or disable communication logging.
        
        Args:
            enable: Whether to enable logging
        """
        if not enable:
            self.communication_history.clear()


class MultiAgentAttentionCommunication(nn.Module):
    """Multi-agent attention communication system."""
    
    def __init__(
        self,
        config: Dict[str, Any],
        obs_dim: int,
        num_agents: int
    ):
        """
        Initialize multi-agent attention communication.
        
        Args:
            config: Communication configuration
            obs_dim: Observation dimension
            num_agents: Number of agents
        """
        super().__init__()
        
        self.num_agents = num_agents
        self.obs_dim = obs_dim
        
        # Create individual communication modules for each agent
        self.comm_modules = nn.ModuleList([
            AttentionCommunication(config, obs_dim)
            for _ in range(num_agents)
        ])
    
    def forward(
        self,
        observations: torch.Tensor,
        positions: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, List[Dict[str, Any]]]:
        """
        Perform communication for all agents.
        
        Args:
            observations: All observations [batch_size, num_agents, obs_dim]
            positions: Agent positions [batch_size, num_agents, 2]
            
        Returns:
            Tuple of (communicated_observations, info_list)
        """
        batch_size, num_agents, obs_dim = observations.size()
        
        communicated_obs = []
        info_list = []
        
        for i in range(num_agents):
            # Get own observation
            own_obs = observations[:, i, :]
            
            # Get other observations
            other_indices = [j for j in range(num_agents) if j != i]
            other_obs = [observations[:, j, :] for j in other_indices]
            
            # Get positions if provided
            agent_positions = None
            if positions is not None:
                agent_positions = positions[:, [i] + other_indices, :]
            
            # Perform communication
            comm_obs, info = self.comm_modules[i](own_obs, other_obs, agent_positions)
            
            communicated_obs.append(comm_obs)
            info_list.append(info)
        
        # Stack communicated observations
        communicated_obs = torch.stack(communicated_obs, dim=1)
        
        return communicated_obs, info_list
    
    def reset(self) -> None:
        """Reset all communication modules."""
        for module in self.comm_modules:
            module.reset()
    
    def get_global_communication_stats(self) -> Dict[str, Any]:
        """Get global communication statistics."""
        all_stats = [module.get_communication_stats() for module in self.comm_modules]
        
        if not all_stats or not all_stats[0]:
            return {}
        
        # Aggregate statistics
        global_stats = {}
        for key in all_stats[0].keys():
            values = [stats[key] for stats in all_stats if key in stats]
            if values:
                global_stats[f"avg_{key}"] = sum(values) / len(values)
                global_stats[f"max_{key}"] = max(values)
                global_stats[f"min_{key}"] = min(values)
        
        return global_stats
