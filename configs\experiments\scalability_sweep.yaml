# Scalability experiment configuration
# Tests performance across different numbers of agents

# Inherit from base config
defaults:
  - ../config

# Override experiment settings
experiment:
  name: "scalability_sweep"
  description: "Evaluate scalability across different agent counts"
  
# Sweep parameters
sweep:
  agent_counts: [2, 3, 5, 8, 10, 15, 20]
  seeds: [0, 1, 2, 3, 4]
  
# Environment settings for scalability
environment:
  name: "mpe_spread"
  max_episode_steps: 150
  
  # Adjust world size based on agent count
  mpe:
    world_size: 3.0  # Larger world for more agents
    
# Training adjustments for scalability
training:
  total_timesteps: 500000  # Shorter training for sweep
  batch_size: 512  # Larger batch for more agents
  eval_freq: 25000
  
# Agent configuration optimized for scalability
agent:
  attention:
    embed_dim: 64
    num_heads: 8  # More heads for better scalability
    max_agents: 25
    
  communication:
    message_dim: 32
    
# Metrics specific to scalability analysis
logging:
  metrics:
    - "episode_reward"
    - "episode_length"
    - "coordination_score"
    - "training_time_per_step"
    - "memory_usage"
    - "communication_overhead"
    - "attention_sparsity"
    - "skill_diversity"
    
# Resource monitoring
monitoring:
  track_memory: true
  track_compute_time: true
  track_communication_cost: true
  
# Analysis settings
analysis:
  plot_learning_curves: true
  plot_scalability_metrics: true
  statistical_tests: true
  
  # Specific plots for scalability
  plots:
    - "reward_vs_agents"
    - "training_time_vs_agents"
    - "memory_vs_agents"
    - "communication_efficiency_vs_agents"
