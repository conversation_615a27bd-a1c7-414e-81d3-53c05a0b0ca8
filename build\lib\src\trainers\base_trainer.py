"""
Abstract base trainer class for reinforcement learning.

This module defines the interface that all trainers must implement,
providing a consistent API for training and evaluation.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
import torch
import numpy as np
import time
from pathlib import Path

from src.utils.logger import TrainingLogger
from src.utils.metrics import MetricsTracker


class BaseTrainer(ABC):
    """Abstract base class for all trainers."""
    
    def __init__(
        self,
        agent,
        env,
        config: Dict[str, Any],
        device: torch.device
    ):
        """
        Initialize base trainer.
        
        Args:
            agent: Agent to train
            env: Environment to train in
            config: Training configuration
            device: Device for computations
        """
        self.agent = agent
        self.env = env
        self.config = config
        self.device = device
        
        # Training state
        self.step_count = 0
        self.episode_count = 0
        self.best_performance = -np.inf
        
        # Configuration
        self.total_timesteps = config["training"]["total_timesteps"]
        self.eval_freq = config["training"]["eval_freq"]
        self.eval_episodes = config["training"]["eval_episodes"]
        self.save_interval = config["checkpoint"]["save_interval"]
        
        # Logging
        self.logger = TrainingLogger(
            log_dir=config["experiment"]["log_dir"],
            experiment_name=config["experiment"]["name"],
            log_interval=config["logging"]["log_interval"]
        )
        
        # Metrics tracking
        self.metrics_tracker = MetricsTracker(
            metrics_list=config["logging"]["metrics"]
        )
        
        # Checkpoint management
        self.save_dir = Path(config["experiment"]["save_dir"]) / config["experiment"]["name"]
        self.save_dir.mkdir(parents=True, exist_ok=True)
        
    @abstractmethod
    def train(self) -> None:
        """Main training loop."""
        pass
    
    @abstractmethod
    def train_step(self) -> Dict[str, float]:
        """Single training step."""
        pass
    
    @abstractmethod
    def evaluate(self, num_episodes: int = None) -> Dict[str, float]:
        """Evaluate agent performance."""
        pass
    
    def collect_episode(self, deterministic: bool = False) -> Dict[str, Any]:
        """
        Collect a single episode of experience.
        
        Args:
            deterministic: Whether to use deterministic policy
            
        Returns:
            Episode data dictionary
        """
        observations = []
        actions = []
        rewards = []
        dones = []
        infos = []
        
        # Reset environment
        obs_list = self.env.reset()
        episode_reward = 0.0
        episode_length = 0
        
        while True:
            # Get actions from agent
            agent_actions = []
            agent_infos = []
            
            for i, obs in enumerate(obs_list):
                # Get other observations for communication
                other_obs = [obs_list[j] for j in range(len(obs_list)) if j != i]
                
                action, info = self.agent.act(
                    observation=obs,
                    other_observations=other_obs,
                    deterministic=deterministic
                )
                
                agent_actions.append(action)
                agent_infos.append(info)
            
            # Store experience
            observations.append(obs_list.copy())
            actions.append(agent_actions.copy())
            infos.append(agent_infos.copy())
            
            # Take environment step
            next_obs_list, reward_list, done_list, env_info = self.env.step(agent_actions)
            
            rewards.append(reward_list.copy())
            dones.append(done_list.copy())
            
            # Update counters
            episode_reward += sum(reward_list)
            episode_length += 1
            
            # Check termination
            if any(done_list) or env_info.get("episode_done", False):
                break
            
            obs_list = next_obs_list
        
        return {
            "observations": observations,
            "actions": actions,
            "rewards": rewards,
            "dones": dones,
            "infos": infos,
            "episode_reward": episode_reward,
            "episode_length": episode_length
        }
    
    def save_checkpoint(self, suffix: str = "") -> str:
        """
        Save training checkpoint.
        
        Args:
            suffix: Optional suffix for checkpoint filename
            
        Returns:
            Path to saved checkpoint
        """
        checkpoint_name = f"checkpoint_step_{self.step_count}"
        if suffix:
            checkpoint_name += f"_{suffix}"
        checkpoint_name += ".pt"
        
        checkpoint_path = self.save_dir / checkpoint_name
        
        # Save agent
        self.agent.save_checkpoint(str(checkpoint_path))
        
        # Save trainer state
        trainer_state = {
            "step_count": self.step_count,
            "episode_count": self.episode_count,
            "best_performance": self.best_performance,
            "config": self.config
        }
        
        trainer_checkpoint_path = self.save_dir / f"trainer_{checkpoint_name}"
        torch.save(trainer_state, trainer_checkpoint_path)
        
        self.logger.log_checkpoint_save(str(checkpoint_path))
        return str(checkpoint_path)
    
    def load_checkpoint(self, checkpoint_path: str) -> None:
        """
        Load training checkpoint.
        
        Args:
            checkpoint_path: Path to checkpoint file
        """
        # Load agent
        self.agent.load_checkpoint(checkpoint_path)
        
        # Load trainer state
        trainer_checkpoint_path = checkpoint_path.replace("checkpoint_", "trainer_checkpoint_")
        if Path(trainer_checkpoint_path).exists():
            trainer_state = torch.load(trainer_checkpoint_path, map_location=self.device)
            self.step_count = trainer_state["step_count"]
            self.episode_count = trainer_state["episode_count"]
            self.best_performance = trainer_state["best_performance"]
    
    def should_evaluate(self) -> bool:
        """Check if evaluation should be performed."""
        return self.step_count % self.eval_freq == 0
    
    def should_save_checkpoint(self) -> bool:
        """Check if checkpoint should be saved."""
        return self.step_count % self.save_interval == 0
    
    def log_training_step(self, metrics: Dict[str, float]) -> None:
        """Log training step metrics."""
        self.metrics_tracker.update(metrics)
        self.logger.log_step(metrics, self.step_count, self.episode_count)
    
    def log_evaluation(self, eval_metrics: Dict[str, float]) -> None:
        """Log evaluation results."""
        self.logger.log_evaluation(eval_metrics)
        
        # Check if this is the best performance
        current_performance = eval_metrics.get("episode_reward", -np.inf)
        if current_performance > self.best_performance:
            self.best_performance = current_performance
            self.save_checkpoint("best")
    
    def get_training_stats(self) -> Dict[str, Any]:
        """Get training statistics."""
        return {
            "step_count": self.step_count,
            "episode_count": self.episode_count,
            "best_performance": self.best_performance,
            "metrics_history": self.metrics_tracker.get_history()
        }
    
    def cleanup(self) -> None:
        """Cleanup resources."""
        if hasattr(self.env, 'close'):
            self.env.close()
        
        self.logger.log_experiment_end()
