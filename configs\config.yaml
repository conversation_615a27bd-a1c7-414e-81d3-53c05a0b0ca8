# Default configuration for Scalable Decentralized HRL
# This file contains the base configuration that can be overridden by experiment-specific configs

# Experiment settings
experiment:
  name: "default_hrl_experiment"
  seed: 42
  num_seeds: 5
  save_dir: "data/checkpoints"
  log_dir: "data/logs"
  device: "auto"  # auto, cpu, cuda
  
# Environment configuration
environment:
  name: "mpe_spread"  # mpe_spread, mpe_tag, moving_goals, custom_swarm
  num_agents: 5
  max_episode_steps: 100
  render: false
  record_video: false
  
  # Environment-specific parameters
  mpe:
    world_size: 2.0
    agent_size: 0.05
    landmark_size: 0.05
    collision_penalty: -1.0
    
  moving_goals:
    goal_change_freq: 50
    num_goals: 3
    goal_reward: 10.0
    
# Agent configuration
agent:
  # Hierarchical structure
  hierarchical: true
  num_skills: 8
  skill_length: 10  # Number of low-level steps per skill
  
  # Network architectures
  high_level_policy:
    hidden_dims: [128, 128]
    activation: "relu"
    output_activation: "tanh"
    
  low_level_policy:
    hidden_dims: [64, 64]
    activation: "relu"
    output_activation: "tanh"
    
  value_network:
    hidden_dims: [128, 64]
    activation: "relu"
    
  # Attention mechanism
  attention:
    enabled: true
    embed_dim: 64
    num_heads: 4
    dropout: 0.1
    max_agents: 20
    
  # Communication
  communication:
    type: "attention"  # attention, mean_pool, none
    message_dim: 32
    range_limit: null  # null for global, float for distance-based
    
# Training configuration
training:
  algorithm: "ppo"  # ppo, a2c
  total_timesteps: 1000000
  learning_rate: 3e-4
  batch_size: 256
  num_epochs: 10
  clip_range: 0.2
  entropy_coef: 0.01
  value_loss_coef: 0.5
  max_grad_norm: 0.5
  
  # Hierarchical training
  high_level_lr: 3e-4
  low_level_lr: 1e-3
  skill_regularization: 0.01
  
  # Experience replay (if applicable)
  buffer_size: 100000
  min_buffer_size: 1000
  
  # Evaluation
  eval_freq: 10000
  eval_episodes: 10
  
# Logging and monitoring
logging:
  level: "INFO"
  log_interval: 1000
  save_interval: 50000
  tensorboard: true
  wandb: false
  
  # Metrics to track
  metrics:
    - "episode_reward"
    - "episode_length"
    - "coordination_score"
    - "skill_usage"
    - "attention_entropy"
    - "communication_efficiency"
    
# Checkpointing
checkpoint:
  save_best: true
  save_last: true
  save_interval: 100000
  max_checkpoints: 5
  
# Distributed training (optional)
distributed:
  enabled: false
  num_workers: 4
  backend: "nccl"  # nccl, gloo
  
# Hyperparameter optimization
optimization:
  enabled: false
  method: "optuna"  # optuna, ray_tune
  num_trials: 100
  
  # Search space
  search_space:
    learning_rate: [1e-5, 1e-2]
    batch_size: [64, 512]
    entropy_coef: [0.001, 0.1]
    attention_heads: [2, 8]
    
# Reproducibility
reproducibility:
  deterministic: false
  benchmark: true
  
# Debug settings
debug:
  enabled: false
  profile: false
  detect_anomaly: false
