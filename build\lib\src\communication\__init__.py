"""
Communication modules for multi-agent coordination.

This module provides different communication mechanisms:
- Attention-based communication
- Mean pooling baseline
- Communication utilities
"""

from .attention_comm import AttentionCommunication
from .mean_pool_comm import MeanPoolCommunication
from .comm_utils import CommunicationManager

__all__ = [
    "AttentionCommunication",
    "MeanPoolCommunication", 
    "CommunicationManager"
]
