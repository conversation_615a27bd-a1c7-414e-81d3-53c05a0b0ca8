#!/usr/bin/env python3
"""
Test runner script for the HRL project.

This script provides convenient ways to run different types of tests
with various configurations and reporting options.
"""

import argparse
import subprocess
import sys
from pathlib import Path


def run_command(cmd, description=""):
    """Run a command and handle errors."""
    print(f"\n{'='*60}")
    if description:
        print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    print('='*60)
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"\n✅ {description or 'Command'} completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n❌ {description or 'Command'} failed with exit code {e.returncode}")
        return False
    except FileNotFoundError:
        print(f"\n❌ Command not found: {cmd[0]}")
        print("Make sure pytest is installed: pip install pytest")
        return False


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(
        description="Run tests for the HRL project",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_tests.py                    # Run all tests
  python run_tests.py --unit             # Run only unit tests
  python run_tests.py --integration      # Run only integration tests
  python run_tests.py --fast             # Skip slow tests
  python run_tests.py --coverage         # Run with coverage report
  python run_tests.py --verbose          # Verbose output
  python run_tests.py --file tests/test_agents.py  # Run specific file
        """
    )
    
    # Test selection options
    parser.add_argument("--unit", action="store_true", 
                       help="Run only unit tests")
    parser.add_argument("--integration", action="store_true", 
                       help="Run only integration tests")
    parser.add_argument("--fast", action="store_true", 
                       help="Skip slow tests")
    parser.add_argument("--gpu", action="store_true", 
                       help="Run only GPU tests")
    parser.add_argument("--mpe", action="store_true", 
                       help="Run only MPE environment tests")
    
    # Output options
    parser.add_argument("--verbose", "-v", action="store_true", 
                       help="Verbose output")
    parser.add_argument("--quiet", "-q", action="store_true", 
                       help="Quiet output")
    parser.add_argument("--coverage", action="store_true", 
                       help="Run with coverage report")
    parser.add_argument("--html-coverage", action="store_true", 
                       help="Generate HTML coverage report")
    
    # Specific test selection
    parser.add_argument("--file", type=str, 
                       help="Run specific test file")
    parser.add_argument("--test", type=str, 
                       help="Run specific test function")
    parser.add_argument("--keyword", "-k", type=str, 
                       help="Run tests matching keyword")
    
    # Other options
    parser.add_argument("--parallel", "-n", type=int, 
                       help="Run tests in parallel (requires pytest-xdist)")
    parser.add_argument("--failfast", "-x", action="store_true", 
                       help="Stop on first failure")
    parser.add_argument("--last-failed", "--lf", action="store_true", 
                       help="Run only tests that failed last time")
    parser.add_argument("--pdb", action="store_true", 
                       help="Drop into debugger on failures")
    
    args = parser.parse_args()
    
    # Build pytest command
    cmd = ["python", "-m", "pytest"]
    
    # Add test selection
    if args.unit:
        cmd.extend(["-m", "unit"])
    elif args.integration:
        cmd.extend(["-m", "integration"])
    elif args.fast:
        cmd.extend(["-m", "not slow"])
    elif args.gpu:
        cmd.extend(["-m", "gpu"])
    elif args.mpe:
        cmd.extend(["-m", "mpe"])
    
    # Add specific file or test
    if args.file:
        cmd.append(args.file)
        if args.test:
            cmd.append(f"::{args.test}")
    elif args.test:
        cmd.extend(["-k", args.test])
    
    # Add keyword filter
    if args.keyword:
        cmd.extend(["-k", args.keyword])
    
    # Add output options
    if args.verbose:
        cmd.append("-v")
    elif args.quiet:
        cmd.append("-q")
    
    # Add coverage
    if args.coverage or args.html_coverage:
        cmd.extend(["--cov=src", "--cov-report=term-missing"])
        if args.html_coverage:
            cmd.append("--cov-report=html")
    
    # Add parallel execution
    if args.parallel:
        cmd.extend(["-n", str(args.parallel)])
    
    # Add other options
    if args.failfast:
        cmd.append("-x")
    if args.last_failed:
        cmd.append("--lf")
    if args.pdb:
        cmd.append("--pdb")
    
    # Run the tests
    success = run_command(cmd, "Running tests")
    
    if success:
        print("\n🎉 All tests passed!")
        
        # Show coverage report location if generated
        if args.html_coverage:
            coverage_dir = Path("htmlcov")
            if coverage_dir.exists():
                print(f"\n📊 HTML coverage report generated: {coverage_dir / 'index.html'}")
    else:
        print("\n💥 Some tests failed!")
        sys.exit(1)


def run_quick_tests():
    """Run a quick subset of tests for development."""
    print("Running quick development tests...")
    
    quick_tests = [
        # Unit tests (fast)
        ["python", "-m", "pytest", "-m", "unit and not slow", "-v"],
        # Basic integration test
        ["python", "-m", "pytest", "tests/integration/test_training_loop.py::TestTrainingLoop::test_single_episode_collection", "-v"]
    ]
    
    all_passed = True
    for cmd in quick_tests:
        if not run_command(cmd):
            all_passed = False
    
    if all_passed:
        print("\n✅ Quick tests passed! Ready for development.")
    else:
        print("\n❌ Some quick tests failed. Check the output above.")
        sys.exit(1)


def run_ci_tests():
    """Run tests suitable for CI/CD."""
    print("Running CI/CD test suite...")
    
    ci_cmd = [
        "python", "-m", "pytest",
        "--cov=src",
        "--cov-report=xml",
        "--cov-report=term-missing",
        "-v",
        "--tb=short"
    ]
    
    success = run_command(ci_cmd, "CI/CD tests")
    
    if success:
        print("\n✅ CI/CD tests passed!")
    else:
        print("\n❌ CI/CD tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    # Check if specific quick commands are requested
    if len(sys.argv) > 1:
        if sys.argv[1] == "quick":
            run_quick_tests()
            sys.exit(0)
        elif sys.argv[1] == "ci":
            run_ci_tests()
            sys.exit(0)
    
    main()
