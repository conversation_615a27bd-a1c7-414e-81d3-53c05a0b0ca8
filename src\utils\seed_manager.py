"""
Random seed management utilities.

This module provides utilities for setting and managing random seeds
across different libraries to ensure reproducible experiments.
"""

import random
import numpy as np
import torch
from typing import Optional, Dict, Any
import logging


def set_global_seed(seed: int) -> None:
    """
    Set random seed for all relevant libraries.
    
    Args:
        seed: Random seed value
    """
    # Python random
    random.seed(seed)
    
    # NumPy
    np.random.seed(seed)
    
    # PyTorch
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    
    # Additional PyTorch settings for reproducibility
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    
    logging.info(f"Global random seed set to: {seed}")


def get_random_state() -> Dict[str, Any]:
    """
    Get current random state from all libraries.
    
    Returns:
        Dictionary containing random states
    """
    return {
        "python_random": random.getstate(),
        "numpy_random": np.random.get_state(),
        "torch_random": torch.get_rng_state(),
        "torch_cuda_random": torch.cuda.get_rng_state() if torch.cuda.is_available() else None
    }


def set_random_state(state: Dict[str, Any]) -> None:
    """
    Set random state for all libraries.
    
    Args:
        state: Dictionary containing random states
    """
    if "python_random" in state:
        random.setstate(state["python_random"])
    
    if "numpy_random" in state:
        np.random.set_state(state["numpy_random"])
    
    if "torch_random" in state:
        torch.set_rng_state(state["torch_random"])
    
    if "torch_cuda_random" in state and state["torch_cuda_random"] is not None:
        if torch.cuda.is_available():
            torch.cuda.set_rng_state(state["torch_cuda_random"])


class SeedManager:
    """Manager for handling random seeds in experiments."""
    
    def __init__(self, base_seed: int = 42):
        """
        Initialize seed manager.
        
        Args:
            base_seed: Base seed for generating other seeds
        """
        self.base_seed = base_seed
        self.current_seed = base_seed
        self.seed_history = []
        
        # Set initial seed
        set_global_seed(base_seed)
    
    def get_seed(self, increment: bool = True) -> int:
        """
        Get next seed value.
        
        Args:
            increment: Whether to increment internal counter
            
        Returns:
            Seed value
        """
        seed = self.current_seed
        
        if increment:
            self.current_seed += 1
            self.seed_history.append(seed)
        
        return seed
    
    def set_seed(self, seed: int) -> None:
        """
        Set specific seed value.
        
        Args:
            seed: Seed value to set
        """
        self.current_seed = seed
        set_global_seed(seed)
        self.seed_history.append(seed)
    
    def reset_to_base(self) -> None:
        """Reset to base seed."""
        self.current_seed = self.base_seed
        set_global_seed(self.base_seed)
    
    def get_experiment_seeds(self, num_seeds: int) -> list:
        """
        Generate seeds for multiple experiment runs.
        
        Args:
            num_seeds: Number of seeds to generate
            
        Returns:
            List of seed values
        """
        seeds = []
        for i in range(num_seeds):
            seeds.append(self.base_seed + i)
        
        return seeds
    
    def save_state(self) -> Dict[str, Any]:
        """
        Save current seed manager state.
        
        Returns:
            State dictionary
        """
        return {
            "base_seed": self.base_seed,
            "current_seed": self.current_seed,
            "seed_history": self.seed_history.copy(),
            "random_state": get_random_state()
        }
    
    def load_state(self, state: Dict[str, Any]) -> None:
        """
        Load seed manager state.
        
        Args:
            state: State dictionary
        """
        self.base_seed = state["base_seed"]
        self.current_seed = state["current_seed"]
        self.seed_history = state["seed_history"].copy()
        
        if "random_state" in state:
            set_random_state(state["random_state"])
