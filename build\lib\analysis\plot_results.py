"""
Result plotting and visualization utilities.

This module provides functions for creating publication-ready plots
and visualizations of experimental results.
"""

import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd
import json
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import argparse


def plot_learning_curves(
    results_dir: str,
    output_dir: str = None,
    metrics: List[str] = None,
    smoothing_window: int = 100
) -> None:
    """
    Plot learning curves from experimental results.
    
    Args:
        results_dir: Directory containing experimental results
        output_dir: Output directory for plots
        metrics: List of metrics to plot
        smoothing_window: Window size for smoothing curves
    """
    if metrics is None:
        metrics = ["episode_reward", "episode_length", "coordination_score"]
    
    if output_dir is None:
        output_dir = Path(results_dir) / "figures"
    
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Set style
    plt.style.use('seaborn-v0_8')
    sns.set_palette("husl")
    
    # Load results
    results_path = Path(results_dir)
    
    # Find all experiment directories
    experiment_dirs = [d for d in results_path.iterdir() if d.is_dir()]
    
    for metric in metrics:
        plt.figure(figsize=(10, 6))
        
        for exp_dir in experiment_dirs:
            # Load metrics data
            metrics_file = exp_dir / "metrics.csv"
            if metrics_file.exists():
                df = pd.read_csv(metrics_file)
                
                if metric in df.columns:
                    # Smooth the curve
                    values = df[metric].rolling(window=smoothing_window, min_periods=1).mean()
                    steps = df.index
                    
                    plt.plot(steps, values, label=exp_dir.name, alpha=0.8)
        
        plt.xlabel("Training Steps")
        plt.ylabel(metric.replace("_", " ").title())
        plt.title(f"Learning Curves: {metric.replace('_', ' ').title()}")
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Save plot
        plot_path = output_dir / f"learning_curve_{metric}.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Saved learning curve plot: {plot_path}")


def plot_scalability_results(
    results_file: str,
    output_dir: str = None,
    metrics: List[str] = None
) -> None:
    """
    Plot scalability results showing performance vs number of agents.
    
    Args:
        results_file: Path to scalability results JSON file
        output_dir: Output directory for plots
        metrics: List of metrics to plot
    """
    if metrics is None:
        metrics = ["final_reward", "training_time", "memory_usage"]
    
    if output_dir is None:
        output_dir = Path(results_file).parent / "figures"
    
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Load results
    with open(results_file, 'r') as f:
        results = json.load(f)
    
    # Set style
    plt.style.use('seaborn-v0_8')
    
    # Extract data
    agent_counts = sorted([int(k) for k in results.keys()])
    
    for metric in metrics:
        if metric not in ["final_reward", "training_time", "memory_usage"]:
            continue
        
        means = []
        stds = []
        
        for agent_count in agent_counts:
            data = results[str(agent_count)]
            if metric in data:
                means.append(data[metric]["mean"])
                stds.append(data[metric]["std"])
            else:
                means.append(0)
                stds.append(0)
        
        # Create plot
        plt.figure(figsize=(10, 6))
        
        plt.errorbar(agent_counts, means, yerr=stds, 
                    marker='o', capsize=5, capthick=2, linewidth=2)
        
        plt.xlabel("Number of Agents")
        plt.ylabel(metric.replace("_", " ").title())
        plt.title(f"Scalability: {metric.replace('_', ' ').title()} vs Number of Agents")
        plt.grid(True, alpha=0.3)
        
        # Add trend line
        if len(agent_counts) > 2:
            z = np.polyfit(agent_counts, means, 1)
            p = np.poly1d(z)
            plt.plot(agent_counts, p(agent_counts), "--", alpha=0.7, 
                    label=f"Trend (slope: {z[0]:.3f})")
            plt.legend()
        
        # Save plot
        plot_path = output_dir / f"scalability_{metric}.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Saved scalability plot: {plot_path}")


def plot_attention_heatmap(
    attention_weights: np.ndarray,
    output_path: str,
    agent_names: List[str] = None
) -> None:
    """
    Plot attention weights as a heatmap.
    
    Args:
        attention_weights: Attention weights matrix [num_agents, num_agents]
        output_path: Path to save the plot
        agent_names: Names of agents for labels
    """
    plt.figure(figsize=(8, 6))
    
    if agent_names is None:
        agent_names = [f"Agent {i}" for i in range(attention_weights.shape[0])]
    
    sns.heatmap(
        attention_weights,
        annot=True,
        fmt='.3f',
        cmap='Blues',
        xticklabels=agent_names,
        yticklabels=agent_names,
        cbar_kws={'label': 'Attention Weight'}
    )
    
    plt.title("Agent Attention Patterns")
    plt.xlabel("Attended Agent")
    plt.ylabel("Attending Agent")
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"Saved attention heatmap: {output_path}")


def plot_comparison_bar(
    results: Dict[str, Dict[str, float]],
    metric: str,
    output_path: str,
    title: str = None
) -> None:
    """
    Plot comparison bar chart for different methods.
    
    Args:
        results: Dictionary of method_name -> {metric: value, metric_std: std}
        metric: Metric to plot
        output_path: Path to save the plot
        title: Plot title
    """
    methods = list(results.keys())
    values = [results[method].get(metric, 0) for method in methods]
    errors = [results[method].get(f"{metric}_std", 0) for method in methods]
    
    plt.figure(figsize=(10, 6))
    
    bars = plt.bar(methods, values, yerr=errors, capsize=5, alpha=0.8)
    
    # Color bars
    colors = plt.cm.Set3(np.linspace(0, 1, len(methods)))
    for bar, color in zip(bars, colors):
        bar.set_color(color)
    
    plt.xlabel("Method")
    plt.ylabel(metric.replace("_", " ").title())
    
    if title is None:
        title = f"Comparison: {metric.replace('_', ' ').title()}"
    plt.title(title)
    
    plt.xticks(rotation=45, ha='right')
    plt.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"Saved comparison plot: {output_path}")


def main():
    """Main function for command-line usage."""
    parser = argparse.ArgumentParser(description="Plot experimental results")
    
    parser.add_argument("--results-dir", type=str, required=True,
                       help="Directory containing experimental results")
    parser.add_argument("--output-dir", type=str,
                       help="Output directory for plots")
    parser.add_argument("--plot-type", choices=["learning", "scalability", "all"],
                       default="all", help="Type of plots to generate")
    parser.add_argument("--metrics", nargs="+",
                       help="Specific metrics to plot")
    
    args = parser.parse_args()
    
    if args.plot_type in ["learning", "all"]:
        plot_learning_curves(
            results_dir=args.results_dir,
            output_dir=args.output_dir,
            metrics=args.metrics
        )
    
    if args.plot_type in ["scalability", "all"]:
        # Look for scalability results file
        scalability_file = Path(args.results_dir) / "scalability_summary.json"
        if scalability_file.exists():
            plot_scalability_results(
                results_file=str(scalability_file),
                output_dir=args.output_dir,
                metrics=args.metrics
            )
        else:
            print(f"Scalability results file not found: {scalability_file}")


if __name__ == "__main__":
    main()
