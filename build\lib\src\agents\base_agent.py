"""
Abstract base agent class for multi-agent reinforcement learning.

This module defines the interface that all agents must implement,
providing a consistent API for training and evaluation.
"""

from abc import ABC, abstractmethod
from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional, List
import torch
import numpy as np
from gym import spaces


class BaseAgent(ABC):
    """Abstract base class for all agents."""
    
    def __init__(
        self,
        config: Dict[str, Any],
        observation_space: spaces.Space,
        action_space: spaces.Space,
        agent_id: Optional[int] = None
    ):
        """
        Initialize the base agent.
        
        Args:
            config: Agent configuration dictionary
            observation_space: Environment observation space
            action_space: Environment action space
            agent_id: Unique identifier for this agent
        """
        self.config = config
        self.observation_space = observation_space
        self.action_space = action_space
        self.agent_id = agent_id
        
        # Training state
        self.training = True
        self.step_count = 0
        self.episode_count = 0
        
        # Device for computations
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
    @abstractmethod
    def act(
        self, 
        observation: np.ndarray,
        other_observations: Optional[List[np.ndarray]] = None,
        deterministic: bool = False
    ) -> <PERSON><PERSON>[np.n<PERSON><PERSON>, Dict[str, Any]]:
        """
        Select an action given the current observation.
        
        Args:
            observation: Current agent observation
            other_observations: Observations from other agents (for communication)
            deterministic: Whether to use deterministic policy
            
        Returns:
            Tuple of (action, info_dict)
        """
        pass
    
    @abstractmethod
    def update(
        self,
        experiences: Dict[str, Any]
    ) -> Dict[str, float]:
        """
        Update the agent's policy using collected experiences.
        
        Args:
            experiences: Dictionary containing experience data
            
        Returns:
            Dictionary of training metrics
        """
        pass
    
    @abstractmethod
    def save_checkpoint(self, filepath: str) -> None:
        """
        Save agent state to checkpoint file.
        
        Args:
            filepath: Path to save checkpoint
        """
        pass
    
    @abstractmethod
    def load_checkpoint(self, filepath: str) -> None:
        """
        Load agent state from checkpoint file.
        
        Args:
            filepath: Path to checkpoint file
        """
        pass
    
    def train(self) -> None:
        """Set agent to training mode."""
        self.training = True
        
    def eval(self) -> None:
        """Set agent to evaluation mode."""
        self.training = False
        
    def reset(self) -> None:
        """Reset agent state for new episode."""
        self.episode_count += 1
        
    def get_info(self) -> Dict[str, Any]:
        """
        Get agent information and statistics.
        
        Returns:
            Dictionary containing agent info
        """
        return {
            "agent_id": self.agent_id,
            "step_count": self.step_count,
            "episode_count": self.episode_count,
            "training": self.training,
            "device": str(self.device)
        }
    
    def to(self, device: torch.device) -> "BaseAgent":
        """
        Move agent to specified device.
        
        Args:
            device: Target device
            
        Returns:
            Self for chaining
        """
        self.device = device
        return self
    
    def parameters(self):
        """
        Get agent parameters for optimization.
        
        Returns:
            Iterator over agent parameters
        """
        # Default implementation returns empty iterator
        # Subclasses should override if they have trainable parameters
        return iter([])
    
    def state_dict(self) -> Dict[str, Any]:
        """
        Get agent state dictionary.
        
        Returns:
            State dictionary
        """
        return {
            "step_count": self.step_count,
            "episode_count": self.episode_count,
            "config": self.config
        }
    
    def load_state_dict(self, state_dict: Dict[str, Any]) -> None:
        """
        Load agent state from dictionary.
        
        Args:
            state_dict: State dictionary to load
        """
        self.step_count = state_dict.get("step_count", 0)
        self.episode_count = state_dict.get("episode_count", 0)
        
    def __repr__(self) -> str:
        """String representation of agent."""
        return f"{self.__class__.__name__}(agent_id={self.agent_id})"
