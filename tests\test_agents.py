"""
Unit tests for agent implementations.

Tests the hierarchical agent, base agent interface, and random baseline.
"""

import pytest
import torch
import numpy as np
from gym import spaces
from unittest.mock import Mock, patch

from src.agents.base_agent import BaseAgent
from src.agents.hierarchical_agent import HierarchicalAgent
from src.agents.random_agent import RandomAgent


class TestBaseAgent:
    """Test cases for BaseAgent abstract class."""
    
    def test_base_agent_cannot_be_instantiated(self):
        """Test that BaseAgent cannot be instantiated directly."""
        with pytest.raises(TypeError):
            BaseAgent({}, spaces.Box(low=-1, high=1, shape=(4,)), spaces.Box(low=-1, high=1, shape=(2,)))
    
    def test_base_agent_interface(self):
        """Test that BaseAgent defines the correct interface."""
        # Check that abstract methods exist
        assert hasattr(BaseAgent, 'act')
        assert hasattr(BaseAgent, 'update')
        assert hasattr(BaseAgent, 'save_checkpoint')
        assert hasattr(BaseAgent, 'load_checkpoint')


class TestHierarchicalAgent:
    """Test cases for HierarchicalAgent."""
    
    @pytest.fixture
    def agent_config(self):
        """Default agent configuration for testing."""
        return {
            "hierarchical": True,
            "num_skills": 4,
            "skill_length": 5,
            "attention": {
                "enabled": True,
                "embed_dim": 32,
                "num_heads": 2,
                "dropout": 0.1,
                "max_agents": 10
            },
            "high_level_policy": {
                "hidden_dims": [64, 32],
                "activation": "relu"
            },
            "low_level_policy": {
                "hidden_dims": [32, 16],
                "activation": "relu"
            },
            "value_network": {
                "hidden_dims": [64, 32],
                "activation": "relu"
            }
        }
    
    @pytest.fixture
    def observation_space(self):
        """Test observation space."""
        return spaces.Box(low=-np.inf, high=np.inf, shape=(8,), dtype=np.float32)
    
    @pytest.fixture
    def action_space(self):
        """Test action space."""
        return spaces.Box(low=-1.0, high=1.0, shape=(2,), dtype=np.float32)
    
    @pytest.fixture
    def agent(self, agent_config, observation_space, action_space):
        """Create test agent."""
        return HierarchicalAgent(agent_config, observation_space, action_space, agent_id=0)
    
    def test_agent_initialization(self, agent, agent_config):
        """Test agent initialization."""
        assert agent.agent_id == 0
        assert agent.hierarchical == True
        assert agent.num_skills == 4
        assert agent.skill_length == 5
        assert agent.current_skill is None
        assert agent.skill_steps_remaining == 0
    
    def test_agent_networks_created(self, agent):
        """Test that all networks are properly created."""
        assert hasattr(agent, 'policy')
        assert hasattr(agent, 'value_network')
        assert hasattr(agent, 'communication')
        
        # Check hierarchical components
        assert hasattr(agent.policy, 'high_level_policy')
        assert hasattr(agent.policy, 'low_level_policy')
        assert hasattr(agent, 'high_value_network')
    
    def test_agent_act_single_observation(self, agent):
        """Test agent action selection with single observation."""
        obs = np.random.randn(8)
        action, info = agent.act(obs, deterministic=False)
        
        assert isinstance(action, np.ndarray)
        assert action.shape == (2,)
        assert isinstance(info, dict)
        assert "step_count" in info
    
    def test_agent_act_with_communication(self, agent):
        """Test agent action selection with communication."""
        obs = np.random.randn(8)
        other_obs = [np.random.randn(8) for _ in range(3)]
        
        action, info = agent.act(obs, other_observations=other_obs, deterministic=False)
        
        assert isinstance(action, np.ndarray)
        assert action.shape == (2,)
        assert isinstance(info, dict)
        assert "num_communicating_agents" in info
    
    def test_hierarchical_skill_selection(self, agent):
        """Test hierarchical skill selection and execution."""
        obs = np.random.randn(8)
        
        # First action should select a skill
        action1, info1 = agent.act(obs, deterministic=False)
        assert "new_skill" in info1
        assert "current_skill" in info1
        assert agent.current_skill is not None
        
        # Subsequent actions should use the same skill
        action2, info2 = agent.act(obs, deterministic=False)
        assert info2["current_skill"] == info1["current_skill"]
        assert agent.skill_steps_remaining < agent.skill_length
    
    def test_agent_reset(self, agent):
        """Test agent reset functionality."""
        # Set some state
        agent.current_skill = torch.tensor(1)
        agent.skill_steps_remaining = 3
        agent.step_count = 100
        
        # Reset
        agent.reset()
        
        assert agent.current_skill is None
        assert agent.skill_steps_remaining == 0
        assert agent.episode_count > 0
    
    def test_agent_train_eval_modes(self, agent):
        """Test training and evaluation mode switching."""
        # Default should be training
        assert agent.training == True
        
        # Switch to eval
        agent.eval()
        assert agent.training == False
        
        # Switch back to train
        agent.train()
        assert agent.training == True
    
    def test_agent_device_movement(self, agent):
        """Test moving agent to different devices."""
        original_device = agent.device
        
        # Move to CPU (should work regardless of current device)
        agent.to(torch.device('cpu'))
        assert agent.device == torch.device('cpu')
        
        # Move back to original device
        agent.to(original_device)
        assert agent.device == original_device
    
    def test_agent_parameters(self, agent):
        """Test agent parameter access."""
        params = list(agent.parameters())
        assert len(params) > 0
        
        # Check that all parameters are tensors
        for param in params:
            assert isinstance(param, torch.Tensor)
    
    def test_agent_state_dict(self, agent):
        """Test agent state dictionary."""
        state_dict = agent.state_dict()
        
        assert isinstance(state_dict, dict)
        assert "step_count" in state_dict
        assert "episode_count" in state_dict
        assert "config" in state_dict
    
    def test_agent_update_placeholder(self, agent):
        """Test agent update method (placeholder implementation)."""
        experiences = {
            "observations": np.random.randn(10, 8),
            "actions": np.random.randn(10, 2),
            "rewards": np.random.randn(10),
            "dones": np.random.randn(10)
        }
        
        # Should not raise an error
        metrics = agent.update(experiences)
        assert isinstance(metrics, dict)
    
    @patch('torch.save')
    def test_save_checkpoint(self, mock_save, agent):
        """Test checkpoint saving."""
        filepath = "test_checkpoint.pt"
        agent.save_checkpoint(filepath)
        
        mock_save.assert_called_once()
        args, kwargs = mock_save.call_args
        assert args[1] == filepath
        
        # Check checkpoint contents
        checkpoint = args[0]
        assert "policy_state_dict" in checkpoint
        assert "value_network_state_dict" in checkpoint
        assert "agent_state_dict" in checkpoint
    
    @patch('torch.load')
    def test_load_checkpoint(self, mock_load, agent):
        """Test checkpoint loading."""
        # Mock checkpoint data
        mock_checkpoint = {
            "policy_state_dict": {},
            "value_network_state_dict": {},
            "agent_state_dict": {"step_count": 1000, "episode_count": 50, "config": {}},
            "high_value_network_state_dict": {},
            "communication_state_dict": {}
        }
        mock_load.return_value = mock_checkpoint
        
        filepath = "test_checkpoint.pt"
        
        # Mock the load_state_dict methods to avoid errors
        with patch.object(agent.policy, 'load_state_dict'), \
             patch.object(agent.value_network, 'load_state_dict'), \
             patch.object(agent.high_value_network, 'load_state_dict'), \
             patch.object(agent.communication, 'load_state_dict'):
            
            agent.load_checkpoint(filepath)
        
        mock_load.assert_called_once_with(filepath, map_location=agent.device)


class TestRandomAgent:
    """Test cases for RandomAgent baseline."""
    
    @pytest.fixture
    def observation_space(self):
        return spaces.Box(low=-1, high=1, shape=(4,), dtype=np.float32)
    
    @pytest.fixture
    def action_space(self):
        return spaces.Box(low=-1, high=1, shape=(2,), dtype=np.float32)
    
    def test_random_agent_creation(self, observation_space, action_space):
        """Test RandomAgent can be created."""
        # This test assumes RandomAgent exists - you may need to implement it
        try:
            agent = RandomAgent({}, observation_space, action_space)
            assert agent is not None
        except (ImportError, NameError):
            pytest.skip("RandomAgent not implemented yet")
    
    def test_random_agent_actions(self, observation_space, action_space):
        """Test RandomAgent produces valid random actions."""
        try:
            agent = RandomAgent({}, observation_space, action_space)
            obs = observation_space.sample()
            
            action, info = agent.act(obs)
            
            assert action_space.contains(action)
            assert isinstance(info, dict)
        except (ImportError, NameError):
            pytest.skip("RandomAgent not implemented yet")


class TestAgentIntegration:
    """Integration tests for agent components."""
    
    @pytest.fixture
    def agent_config(self):
        return {
            "hierarchical": True,
            "num_skills": 3,
            "skill_length": 4,
            "attention": {"enabled": True, "embed_dim": 16, "num_heads": 2}
        }
    
    @pytest.fixture
    def agent(self, agent_config):
        obs_space = spaces.Box(low=-1, high=1, shape=(6,), dtype=np.float32)
        action_space = spaces.Box(low=-1, high=1, shape=(2,), dtype=np.float32)
        return HierarchicalAgent(agent_config, obs_space, action_space)
    
    def test_multi_step_episode(self, agent):
        """Test agent behavior over multiple steps."""
        obs = np.random.randn(6)
        actions = []
        infos = []
        
        # Run for multiple steps
        for step in range(10):
            action, info = agent.act(obs, deterministic=False)
            actions.append(action)
            infos.append(info)
            
            # Simulate environment step
            obs = np.random.randn(6)
        
        assert len(actions) == 10
        assert len(infos) == 10
        
        # Check that skills change appropriately
        skills = [info.get("current_skill") for info in infos if "current_skill" in info]
        assert len(skills) > 0
    
    def test_deterministic_vs_stochastic(self, agent):
        """Test deterministic vs stochastic action selection."""
        obs = np.random.randn(6)
        
        # Get deterministic actions
        det_actions = []
        for _ in range(5):
            action, _ = agent.act(obs, deterministic=True)
            det_actions.append(action.copy())
        
        # Get stochastic actions
        stoch_actions = []
        for _ in range(5):
            action, _ = agent.act(obs, deterministic=False)
            stoch_actions.append(action.copy())
        
        # Deterministic actions should be more similar
        det_var = np.var(det_actions, axis=0).mean()
        stoch_var = np.var(stoch_actions, axis=0).mean()
        
        # This is a loose test - in practice, deterministic should have lower variance
        assert det_var >= 0  # Just check it doesn't crash


if __name__ == "__main__":
    pytest.main([__file__])
