"""
Unit tests for environment wrappers and loaders.

Tests environment loading, multi-agent wrappers, and environment utilities.
"""

import pytest
import numpy as np
from gym import spaces
from unittest.mock import Mock, patch, MagicMock

from src.environments.environment_loader import (
    MultiAgentEnvWrapper, 
    load_environment, 
    create_environment
)
from src.environments.env_utils import (
    normalize_observations,
    get_env_info,
    compute_agent_distances,
    check_collisions,
    compute_coverage_metric,
    ObservationNormalizer
)


class TestMultiAgentEnvWrapper:
    """Test cases for MultiAgentEnvWrapper."""
    
    @pytest.fixture
    def mock_env(self):
        """Create mock environment."""
        env = Mock()
        env.observation_space = spaces.Box(low=-1, high=1, shape=(4,), dtype=np.float32)
        env.action_space = spaces.Box(low=-1, high=1, shape=(2,), dtype=np.float32)
        return env
    
    @pytest.fixture
    def env_config(self):
        """Environment configuration."""
        return {
            "num_agents": 3,
            "max_episode_steps": 50,
            "render": False
        }
    
    @pytest.fixture
    def wrapped_env(self, mock_env, env_config):
        """Create wrapped environment."""
        return MultiAgentEnvWrapper(mock_env, env_config)
    
    def test_wrapper_initialization(self, wrapped_env, env_config):
        """Test wrapper initialization."""
        assert wrapped_env.num_agents == 3
        assert wrapped_env.max_episode_steps == 50
        assert wrapped_env.render_mode == False
        assert wrapped_env.episode_step == 0
    
    def test_space_setup_standard(self, wrapped_env):
        """Test space setup for standard environments."""
        assert hasattr(wrapped_env, 'observation_space')
        assert hasattr(wrapped_env, 'action_space')
        assert isinstance(wrapped_env.observation_space, spaces.Box)
        assert isinstance(wrapped_env.action_space, spaces.Box)
    
    def test_space_setup_pettingzoo_style(self, env_config):
        """Test space setup for PettingZoo-style environments."""
        # Mock PettingZoo-style environment
        mock_env = Mock()
        mock_env.observation_spaces = {
            'agent_0': spaces.Box(low=-1, high=1, shape=(4,), dtype=np.float32),
            'agent_1': spaces.Box(low=-1, high=1, shape=(4,), dtype=np.float32)
        }
        mock_env.action_spaces = {
            'agent_0': spaces.Box(low=-1, high=1, shape=(2,), dtype=np.float32),
            'agent_1': spaces.Box(low=-1, high=1, shape=(2,), dtype=np.float32)
        }
        
        wrapped_env = MultiAgentEnvWrapper(mock_env, env_config)
        
        assert isinstance(wrapped_env.observation_space, spaces.Box)
        assert isinstance(wrapped_env.action_space, spaces.Box)
    
    def test_reset_standard_env(self, wrapped_env):
        """Test reset for standard environment."""
        # Mock reset return
        wrapped_env.env.reset.return_value = [
            np.array([1, 2, 3, 4]),
            np.array([5, 6, 7, 8]),
            np.array([9, 10, 11, 12])
        ]
        
        obs_list = wrapped_env.reset()
        
        assert len(obs_list) == 3
        assert all(isinstance(obs, np.ndarray) for obs in obs_list)
        assert wrapped_env.episode_step == 0
    
    def test_reset_pettingzoo_env(self, env_config):
        """Test reset for PettingZoo-style environment."""
        mock_env = Mock()
        mock_env.reset.return_value = {
            'agent_0': np.array([1, 2, 3, 4]),
            'agent_1': np.array([5, 6, 7, 8])
        }
        mock_env.observation_spaces = {
            'agent_0': spaces.Box(low=-1, high=1, shape=(4,), dtype=np.float32),
            'agent_1': spaces.Box(low=-1, high=1, shape=(4,), dtype=np.float32)
        }
        mock_env.action_spaces = {
            'agent_0': spaces.Box(low=-1, high=1, shape=(2,), dtype=np.float32),
            'agent_1': spaces.Box(low=-1, high=1, shape=(2,), dtype=np.float32)
        }
        
        wrapped_env = MultiAgentEnvWrapper(mock_env, env_config)
        obs_list = wrapped_env.reset()
        
        assert len(obs_list) == 2
        assert np.array_equal(obs_list[0], np.array([1, 2, 3, 4]))
        assert np.array_equal(obs_list[1], np.array([5, 6, 7, 8]))
    
    def test_step_standard_env(self, wrapped_env):
        """Test step for standard environment."""
        actions = [np.array([0.1, 0.2]), np.array([0.3, 0.4]), np.array([0.5, 0.6])]
        
        # Mock step return
        wrapped_env.env.step.return_value = (
            [np.array([1, 2, 3, 4]), np.array([5, 6, 7, 8]), np.array([9, 10, 11, 12])],
            [1.0, 2.0, 3.0],
            [False, False, False],
            {}
        )
        
        obs_list, reward_list, done_list, info = wrapped_env.step(actions)
        
        assert len(obs_list) == 3
        assert len(reward_list) == 3
        assert len(done_list) == 3
        assert wrapped_env.episode_step == 1
        assert "episode_step" in info
    
    def test_step_episode_termination(self, wrapped_env):
        """Test step with episode termination."""
        actions = [np.array([0.1, 0.2]), np.array([0.3, 0.4]), np.array([0.5, 0.6])]
        
        # Set episode step near limit
        wrapped_env.episode_step = wrapped_env.max_episode_steps - 1
        
        wrapped_env.env.step.return_value = (
            [np.array([1, 2, 3, 4]), np.array([5, 6, 7, 8]), np.array([9, 10, 11, 12])],
            [1.0, 2.0, 3.0],
            [False, False, False],
            {}
        )
        
        obs_list, reward_list, done_list, info = wrapped_env.step(actions)
        
        # Should terminate due to max steps
        assert all(done_list)
        assert info["episode_done"] == True
    
    def test_get_env_info(self, wrapped_env):
        """Test get_env_info method."""
        info = wrapped_env.get_env_info()
        
        assert isinstance(info, dict)
        # The actual content depends on get_env_info implementation


class TestEnvironmentLoader:
    """Test cases for environment loading functions."""
    
    def test_create_environment(self):
        """Test create_environment function."""
        # This will create a placeholder environment since MPE might not be available
        env = create_environment("moving_goals", num_agents=3, max_episode_steps=100)
        
        assert isinstance(env, MultiAgentEnvWrapper)
        assert env.num_agents == 3
        assert env.max_episode_steps == 100
    
    def test_load_environment_moving_goals(self):
        """Test loading moving goals environment."""
        config = {
            "name": "moving_goals",
            "num_agents": 4,
            "max_episode_steps": 75
        }
        
        env = load_environment(config)
        
        assert isinstance(env, MultiAgentEnvWrapper)
        assert env.num_agents == 4
        assert env.max_episode_steps == 75
    
    def test_load_environment_custom_swarm(self):
        """Test loading custom swarm environment."""
        config = {
            "name": "custom_swarm",
            "num_agents": 5,
            "max_episode_steps": 200
        }
        
        env = load_environment(config)
        
        assert isinstance(env, MultiAgentEnvWrapper)
        assert env.num_agents == 5
    
    def test_load_environment_invalid_name(self):
        """Test loading environment with invalid name."""
        config = {
            "name": "invalid_environment",
            "num_agents": 3
        }
        
        with pytest.raises(ValueError, match="Unknown environment"):
            load_environment(config)
    
    @patch('src.environments.environment_loader.MPE_AVAILABLE', True)
    @patch('src.environments.environment_loader.simple_spread_v2')
    def test_load_mpe_spread(self, mock_simple_spread):
        """Test loading MPE spread environment."""
        mock_env = Mock()
        mock_simple_spread.env.return_value = mock_env
        
        config = {
            "name": "mpe_spread",
            "num_agents": 3,
            "max_episode_steps": 25
        }
        
        env = load_environment(config)
        
        assert isinstance(env, MultiAgentEnvWrapper)
        mock_simple_spread.env.assert_called_once()


class TestEnvironmentUtils:
    """Test cases for environment utilities."""
    
    def test_normalize_observations(self):
        """Test observation normalization."""
        obs = np.array([[1, 2, 3], [4, 5, 6]])
        obs_mean = np.array([2.5, 3.5, 4.5])
        obs_std = np.array([1.5, 1.5, 1.5])
        
        normalized = normalize_observations(obs, obs_mean, obs_std)
        
        expected = (obs - obs_mean) / (obs_std + 1e-8)
        np.testing.assert_array_almost_equal(normalized, expected)
    
    def test_normalize_observations_no_stats(self):
        """Test normalization without statistics."""
        obs = np.array([[1, 2, 3], [4, 5, 6]])
        
        normalized = normalize_observations(obs)
        
        np.testing.assert_array_equal(normalized, obs)
    
    def test_get_env_info_box_spaces(self):
        """Test get_env_info with Box spaces."""
        mock_env = Mock()
        mock_env.num_agents = 3
        mock_env.max_episode_steps = 100
        mock_env.observation_space = spaces.Box(low=-1, high=1, shape=(4,), dtype=np.float32)
        mock_env.action_space = spaces.Box(low=-2, high=2, shape=(2,), dtype=np.float32)
        
        info = get_env_info(mock_env)
        
        assert info["num_agents"] == 3
        assert info["max_episode_steps"] == 100
        assert info["obs_dim"] == 4
        assert info["action_dim"] == 2
        np.testing.assert_array_equal(info["obs_low"], np.array([-1, -1, -1, -1]))
        np.testing.assert_array_equal(info["action_low"], np.array([-2, -2]))
    
    def test_get_env_info_discrete_action(self):
        """Test get_env_info with discrete action space."""
        mock_env = Mock()
        mock_env.num_agents = 2
        mock_env.observation_space = spaces.Box(low=-1, high=1, shape=(3,), dtype=np.float32)
        mock_env.action_space = spaces.Discrete(4)
        
        info = get_env_info(mock_env)
        
        assert info["action_dim"] == 4
    
    def test_compute_agent_distances(self):
        """Test agent distance computation."""
        positions = np.array([
            [0, 0],
            [1, 0],
            [0, 1]
        ])
        
        distances = compute_agent_distances(positions)
        
        assert distances.shape == (3, 3)
        assert distances[0, 0] == 0  # Distance to self
        assert distances[0, 1] == 1  # Distance between (0,0) and (1,0)
        assert distances[0, 2] == 1  # Distance between (0,0) and (0,1)
        assert np.abs(distances[1, 2] - np.sqrt(2)) < 1e-6  # Distance between (1,0) and (0,1)
    
    def test_check_collisions(self):
        """Test collision detection."""
        # No collisions
        positions = np.array([
            [0, 0],
            [1, 0],
            [0, 1]
        ])
        
        collisions = check_collisions(positions, agent_radius=0.05)
        assert len(collisions) == 0
        
        # With collisions
        close_positions = np.array([
            [0, 0],
            [0.05, 0],  # Very close to first agent
            [1, 1]
        ])
        
        collisions = check_collisions(close_positions, agent_radius=0.05)
        assert len(collisions) > 0
        assert (0, 1) in collisions or (1, 0) in collisions
    
    def test_compute_coverage_metric(self):
        """Test coverage metric computation."""
        # Single agent at center
        positions = np.array([[0, 0]])
        coverage = compute_coverage_metric(positions, (-1, 1))
        
        assert 0 <= coverage <= 1
        
        # Multiple agents spread out
        spread_positions = np.array([
            [-0.5, -0.5],
            [0.5, -0.5],
            [-0.5, 0.5],
            [0.5, 0.5]
        ])
        
        spread_coverage = compute_coverage_metric(spread_positions, (-1, 1))
        
        # Spread out agents should have better coverage
        assert spread_coverage > coverage
    
    def test_compute_coverage_empty(self):
        """Test coverage with no agents."""
        coverage = compute_coverage_metric(np.array([]).reshape(0, 2), (-1, 1))
        assert coverage == 0.0


class TestObservationNormalizer:
    """Test cases for ObservationNormalizer."""
    
    def test_normalizer_initialization(self):
        """Test normalizer initialization."""
        normalizer = ObservationNormalizer(obs_dim=4)
        
        assert normalizer.obs_dim == 4
        assert normalizer.count == 0
        np.testing.assert_array_equal(normalizer.mean, np.zeros(4))
        np.testing.assert_array_equal(normalizer.std, np.ones(4))
    
    def test_normalizer_update(self):
        """Test normalizer statistics update."""
        normalizer = ObservationNormalizer(obs_dim=2)
        
        # First batch
        obs1 = np.array([[1, 2], [3, 4]])
        normalizer.update(obs1)
        
        assert normalizer.count == 2
        np.testing.assert_array_almost_equal(normalizer.mean, np.array([2, 3]))
        
        # Second batch
        obs2 = np.array([[5, 6]])
        normalizer.update(obs2)
        
        assert normalizer.count == 3
        expected_mean = np.array([3, 4])  # (1+3+5)/3, (2+4+6)/3
        np.testing.assert_array_almost_equal(normalizer.mean, expected_mean)
    
    def test_normalizer_normalize(self):
        """Test observation normalization."""
        normalizer = ObservationNormalizer(obs_dim=2)
        
        # Update with some data
        obs = np.array([[0, 0], [2, 4]])
        normalizer.update(obs)
        
        # Normalize new observation
        new_obs = np.array([[1, 2]])
        normalized = normalizer.normalize(new_obs)
        
        expected = (new_obs - normalizer.mean) / normalizer.std
        expected = np.clip(expected, -5, 5)  # Default clip range
        
        np.testing.assert_array_almost_equal(normalized, expected)
    
    def test_normalizer_denormalize(self):
        """Test observation denormalization."""
        normalizer = ObservationNormalizer(obs_dim=2)
        
        # Set some statistics
        normalizer.mean = np.array([1, 2])
        normalizer.std = np.array([0.5, 1.0])
        
        normalized_obs = np.array([[0, 0]])
        denormalized = normalizer.denormalize(normalized_obs)
        
        expected = normalized_obs * normalizer.std + normalizer.mean
        np.testing.assert_array_almost_equal(denormalized, expected)
    
    def test_normalizer_get_set_stats(self):
        """Test getting and setting normalizer statistics."""
        normalizer = ObservationNormalizer(obs_dim=2)
        
        # Update with some data
        obs = np.array([[1, 2], [3, 4]])
        normalizer.update(obs)
        
        # Get stats
        stats = normalizer.get_stats()
        
        assert "mean" in stats
        assert "std" in stats
        assert "var" in stats
        assert "count" in stats
        
        # Create new normalizer and set stats
        new_normalizer = ObservationNormalizer(obs_dim=2)
        new_normalizer.set_stats(stats)
        
        np.testing.assert_array_equal(new_normalizer.mean, normalizer.mean)
        np.testing.assert_array_equal(new_normalizer.std, normalizer.std)
        assert new_normalizer.count == normalizer.count


if __name__ == "__main__":
    pytest.main([__file__])
