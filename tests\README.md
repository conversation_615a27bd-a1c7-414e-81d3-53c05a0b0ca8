# Testing Framework for Scalable Decentralized HRL

This directory contains a comprehensive test suite for the Scalable Decentralized Hierarchical Reinforcement Learning project.

## 📁 Test Structure

```
tests/
├── __init__.py                 # Test package initialization
├── conftest.py                 # Shared fixtures and configuration
├── test_agents.py              # Unit tests for agent implementations
├── test_models.py              # Unit tests for neural network models
├── test_environments.py        # Unit tests for environment wrappers
├── test_algorithms.py          # Unit tests for RL algorithms
├── test_communication.py       # Unit tests for communication modules
├── integration/                # Integration tests
│   ├── test_training_loop.py   # Training loop integration tests
│   └── test_full_pipeline.py   # End-to-end pipeline tests
└── README.md                   # This file
```

## 🚀 Quick Start

### Install Test Dependencies

```bash
pip install pytest pytest-cov pytest-mock
```

### Run All Tests

```bash
# Using pytest directly
pytest

# Using the test runner script
python run_tests.py
```

### Run Specific Test Categories

```bash
# Unit tests only
python run_tests.py --unit

# Integration tests only
python run_tests.py --integration

# Fast tests (skip slow ones)
python run_tests.py --fast

# With coverage report
python run_tests.py --coverage
```

## 🧪 Test Categories

### Unit Tests

Test individual components in isolation:

- **`test_agents.py`**: Agent implementations (HierarchicalAgent, BaseAgent)
- **`test_models.py`**: Neural network models (attention, policies, value networks)
- **`test_environments.py`**: Environment wrappers and utilities
- **`test_algorithms.py`**: RL algorithms (PPO, experience replay)
- **`test_communication.py`**: Communication modules (attention-based communication)

### Integration Tests

Test component interactions and complete workflows:

- **`test_training_loop.py`**: Training loop functionality
- **`test_full_pipeline.py`**: End-to-end pipeline testing

## 🏷️ Test Markers

Tests are categorized using pytest markers:

- `@pytest.mark.unit`: Unit tests for individual components
- `@pytest.mark.integration`: Integration tests for component interactions
- `@pytest.mark.slow`: Tests that take a long time to run
- `@pytest.mark.gpu`: Tests that require GPU
- `@pytest.mark.mpe`: Tests that require MPE environments

### Running Tests by Marker

```bash
# Run only unit tests
pytest -m unit

# Run only fast tests
pytest -m "not slow"

# Run GPU tests (if CUDA available)
pytest -m gpu

# Run MPE tests (if PettingZoo available)
pytest -m mpe
```

## 🔧 Test Configuration

### Pytest Configuration (`pytest.ini`)

- Test discovery patterns
- Output formatting
- Marker definitions
- Warning filters

### Shared Fixtures (`conftest.py`)

- **Environment setup**: Deterministic seeding, device configuration
- **Sample data**: Standard observation/action spaces, batch data
- **Configurations**: Minimal configs for testing
- **Custom assertions**: Helper functions for common test patterns

## 📊 Coverage Reports

### Generate Coverage Reports

```bash
# Terminal coverage report
python run_tests.py --coverage

# HTML coverage report
python run_tests.py --html-coverage

# XML coverage report (for CI)
pytest --cov=src --cov-report=xml
```

### Coverage Targets

- **Unit tests**: Aim for >90% line coverage
- **Integration tests**: Focus on workflow coverage
- **Critical paths**: 100% coverage for core algorithms

## 🚀 Running Tests

### Development Workflow

```bash
# Quick tests during development
python run_tests.py quick

# Run tests for specific file
python run_tests.py --file tests/test_agents.py

# Run specific test function
python run_tests.py --test test_agent_initialization

# Run tests matching keyword
python run_tests.py --keyword "attention"
```

### Continuous Integration

```bash
# CI/CD test suite
python run_tests.py ci

# Parallel execution (if pytest-xdist installed)
python run_tests.py --parallel 4

# Stop on first failure
python run_tests.py --failfast
```

### Debugging Tests

```bash
# Verbose output
python run_tests.py --verbose

# Drop into debugger on failure
python run_tests.py --pdb

# Run only failed tests from last run
python run_tests.py --last-failed
```

## 🧩 Writing New Tests

### Unit Test Template

```python
import pytest
import torch
from src.module_to_test import ClassToTest

class TestClassToTest:
    """Test cases for ClassToTest."""
    
    @pytest.fixture
    def test_instance(self):
        """Create test instance."""
        return ClassToTest(config={})
    
    def test_initialization(self, test_instance):
        """Test proper initialization."""
        assert test_instance is not None
        # Add specific assertions
    
    def test_method_behavior(self, test_instance):
        """Test specific method behavior."""
        result = test_instance.method()
        assert result == expected_value
```

### Integration Test Template

```python
import pytest
from src.component_a import ComponentA
from src.component_b import ComponentB

class TestComponentIntegration:
    """Integration tests for components."""
    
    @pytest.fixture
    def integrated_system(self):
        """Create integrated system."""
        comp_a = ComponentA()
        comp_b = ComponentB()
        return comp_a, comp_b
    
    def test_component_interaction(self, integrated_system):
        """Test components work together."""
        comp_a, comp_b = integrated_system
        
        # Test interaction
        result = comp_a.process(comp_b.get_data())
        assert result is not None
```

### Using Custom Fixtures

```python
def test_with_minimal_config(minimal_agent_config, sample_observation_space):
    """Test using shared fixtures."""
    agent = HierarchicalAgent(
        config=minimal_agent_config,
        observation_space=sample_observation_space,
        action_space=sample_action_space
    )
    assert agent.hierarchical == True
```

## 🔍 Test Best Practices

### 1. Test Organization

- Group related tests in classes
- Use descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)

### 2. Fixtures and Mocking

- Use fixtures for common setup
- Mock external dependencies
- Keep tests isolated and independent

### 3. Assertions

- Use specific assertions
- Test both success and failure cases
- Include edge cases and boundary conditions

### 4. Performance

- Keep unit tests fast (<1 second each)
- Mark slow tests appropriately
- Use minimal configurations for testing

## 🐛 Troubleshooting

### Common Issues

1. **Import Errors**: Ensure `src` is in Python path
2. **CUDA Errors**: Use CPU device for testing
3. **Random Failures**: Check for proper seeding
4. **Slow Tests**: Use minimal configurations

### Debug Commands

```bash
# Run with maximum verbosity
pytest -vvv --tb=long

# Show local variables in tracebacks
pytest --tb=auto --showlocals

# Run specific test with debugging
pytest tests/test_agents.py::TestHierarchicalAgent::test_initialization -vvv --pdb
```

## 📈 Continuous Integration

### GitHub Actions Example

```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.8
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-cov
    - name: Run tests
      run: python run_tests.py ci
```

## 📚 Additional Resources

- [Pytest Documentation](https://docs.pytest.org/)
- [Testing Best Practices](https://docs.python-guide.org/writing/tests/)
- [Mocking in Python](https://docs.python.org/3/library/unittest.mock.html)

## 🤝 Contributing

When adding new features:

1. Write tests first (TDD approach)
2. Ensure >90% coverage for new code
3. Add integration tests for new workflows
4. Update this README if adding new test categories

Happy testing! 🧪✨
