[tool:pytest]
# Pytest configuration for the HRL project

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Output options
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes

# Markers for test categorization
markers =
    unit: Unit tests for individual components
    integration: Integration tests for component interactions
    slow: Tests that take a long time to run
    gpu: Tests that require GPU
    mpe: Tests that require MPE environments
    
# Minimum version
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Coverage options (if pytest-cov is installed)
# addopts = --cov=src --cov-report=html --cov-report=term-missing

# Ignore certain warnings
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
