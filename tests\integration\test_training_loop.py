"""
Integration tests for the training loop.

Tests the complete training workflow including agent-environment interaction,
experience collection, and policy updates.
"""

import pytest
import torch
import numpy as np
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch

from src.agents.hierarchical_agent import HierarchicalAgent
from src.environments.environment_loader import load_environment
from src.trainers.hierarchical_trainer import HierarchicalTrainer
from src.utils.seed_manager import set_global_seed


class TestTrainingLoop:
    """Integration tests for the complete training loop."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test outputs."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def minimal_config(self, temp_dir):
        """Minimal configuration for testing."""
        return {
            "experiment": {
                "name": "test_experiment",
                "seed": 42,
                "save_dir": str(Path(temp_dir) / "checkpoints"),
                "log_dir": str(Path(temp_dir) / "logs"),
                "device": "cpu"
            },
            "environment": {
                "name": "moving_goals",
                "num_agents": 2,
                "max_episode_steps": 10
            },
            "agent": {
                "hierarchical": True,
                "num_skills": 3,
                "skill_length": 3,
                "attention": {
                    "enabled": True,
                    "embed_dim": 16,
                    "num_heads": 2,
                    "dropout": 0.1,
                    "max_agents": 5
                },
                "high_level_policy": {
                    "hidden_dims": [32, 16],
                    "activation": "relu"
                },
                "low_level_policy": {
                    "hidden_dims": [16, 8],
                    "activation": "relu"
                },
                "value_network": {
                    "hidden_dims": [32, 16],
                    "activation": "relu"
                }
            },
            "training": {
                "algorithm": "ppo",
                "total_timesteps": 50,  # Very short for testing
                "learning_rate": 1e-3,
                "batch_size": 4,
                "num_epochs": 2,
                "eval_freq": 25,
                "eval_episodes": 2
            },
            "logging": {
                "level": "INFO",
                "log_interval": 10,
                "tensorboard": False,
                "wandb": False,
                "metrics": ["episode_reward", "episode_length"]
            },
            "checkpoint": {
                "save_best": True,
                "save_last": True,
                "save_interval": 25,
                "max_checkpoints": 2
            }
        }
    
    @pytest.fixture
    def environment(self, minimal_config):
        """Create test environment."""
        return load_environment(minimal_config["environment"])
    
    @pytest.fixture
    def agent(self, minimal_config, environment):
        """Create test agent."""
        return HierarchicalAgent(
            config=minimal_config["agent"],
            observation_space=environment.observation_space,
            action_space=environment.action_space,
            agent_id=0
        )
    
    @pytest.fixture
    def trainer(self, agent, environment, minimal_config):
        """Create test trainer."""
        return HierarchicalTrainer(
            agent=agent,
            env=environment,
            config=minimal_config,
            device=torch.device('cpu')
        )
    
    def test_single_episode_collection(self, trainer):
        """Test collecting a single episode."""
        set_global_seed(42)
        
        episode_data = trainer.collect_episode(deterministic=False)
        
        # Check episode data structure
        assert isinstance(episode_data, dict)
        assert "observations" in episode_data
        assert "actions" in episode_data
        assert "rewards" in episode_data
        assert "dones" in episode_data
        assert "episode_reward" in episode_data
        assert "episode_length" in episode_data
        
        # Check data shapes
        assert len(episode_data["observations"]) == episode_data["episode_length"]
        assert len(episode_data["actions"]) == episode_data["episode_length"]
        assert len(episode_data["rewards"]) == episode_data["episode_length"]
        assert len(episode_data["dones"]) == episode_data["episode_length"]
        
        # Check that episode terminates properly
        assert episode_data["episode_length"] > 0
        assert episode_data["episode_length"] <= trainer.env.max_episode_steps
    
    def test_deterministic_vs_stochastic_episodes(self, trainer):
        """Test deterministic vs stochastic episode collection."""
        set_global_seed(42)
        
        # Collect deterministic episodes
        det_episode1 = trainer.collect_episode(deterministic=True)
        det_episode2 = trainer.collect_episode(deterministic=True)
        
        # Collect stochastic episodes
        stoch_episode1 = trainer.collect_episode(deterministic=False)
        stoch_episode2 = trainer.collect_episode(deterministic=False)
        
        # Deterministic episodes should be more similar than stochastic ones
        # (This is a loose test due to environment randomness)
        assert isinstance(det_episode1["episode_reward"], (int, float))
        assert isinstance(stoch_episode1["episode_reward"], (int, float))
    
    def test_experience_buffer_management(self, trainer):
        """Test experience buffer filling and clearing."""
        set_global_seed(42)
        
        # Initially empty
        assert len(trainer.experience_buffer) == 0
        
        # Collect episodes to fill buffer
        for _ in range(trainer.batch_size):
            episode_data = trainer.collect_episode()
            trainer.experience_buffer.append(episode_data)
        
        assert len(trainer.experience_buffer) == trainer.batch_size
        
        # Clear buffer
        trainer.experience_buffer.clear()
        assert len(trainer.experience_buffer) == 0
    
    def test_training_step(self, trainer):
        """Test a single training step."""
        set_global_seed(42)
        
        # Fill experience buffer
        for _ in range(trainer.batch_size):
            episode_data = trainer.collect_episode()
            trainer.experience_buffer.append(episode_data)
        
        # Perform training step
        with patch.object(trainer.rl_algorithm, 'update') as mock_update:
            mock_update.return_value = {
                "policy_loss": 0.5,
                "value_loss": 0.3,
                "entropy_loss": 0.1
            }
            
            training_metrics = trainer.train_step()
        
        assert isinstance(training_metrics, dict)
        mock_update.assert_called_once()
    
    def test_evaluation(self, trainer):
        """Test agent evaluation."""
        set_global_seed(42)
        
        eval_metrics = trainer.evaluate(num_episodes=2)
        
        assert isinstance(eval_metrics, dict)
        assert "episode_reward" in eval_metrics
        assert "episode_length" in eval_metrics
        assert "episode_reward_std" in eval_metrics
        assert "episode_length_std" in eval_metrics
        
        # Check that metrics are reasonable
        assert isinstance(eval_metrics["episode_reward"], (int, float))
        assert eval_metrics["episode_length"] > 0
    
    def test_checkpoint_saving_loading(self, trainer, temp_dir):
        """Test checkpoint saving and loading."""
        set_global_seed(42)
        
        # Save initial checkpoint
        checkpoint_path = trainer.save_checkpoint("test")
        assert Path(checkpoint_path).exists()
        
        # Modify trainer state
        original_step_count = trainer.step_count
        trainer.step_count = 100
        trainer.episode_count = 10
        
        # Load checkpoint
        trainer.load_checkpoint(checkpoint_path)
        
        # State should be restored (or at least not crash)
        assert isinstance(trainer.step_count, int)
        assert isinstance(trainer.episode_count, int)
    
    def test_metrics_tracking(self, trainer):
        """Test metrics tracking during training."""
        set_global_seed(42)
        
        # Collect episode and track metrics
        episode_data = trainer.collect_episode()
        
        # Mock episode metrics computation
        with patch('src.utils.metrics.compute_episode_metrics') as mock_compute:
            mock_compute.return_value = {
                "episode_reward": episode_data["episode_reward"],
                "episode_length": episode_data["episode_length"],
                "mean_step_reward": 0.1
            }
            
            trainer.log_training_step(mock_compute.return_value)
        
        # Check that metrics were tracked
        assert trainer.metrics_tracker.metrics_history
    
    def test_hierarchical_specific_metrics(self, trainer):
        """Test hierarchical-specific metrics collection."""
        set_global_seed(42)
        
        # Collect episode with hierarchical agent
        episode_data = trainer.collect_episode()
        
        # Add hierarchical metrics
        trainer._add_hierarchical_metrics(episode_data)
        
        # Check for hierarchical-specific data
        if "infos" in episode_data and episode_data["infos"]:
            # Should have skill usage and attention metrics
            assert "skill_usage" in episode_data
            assert "attention_entropies" in episode_data
            assert "communication_efficiencies" in episode_data


class TestTrainingIntegration:
    """Integration tests for complete training workflows."""
    
    @pytest.fixture
    def very_minimal_config(self, tmp_path):
        """Ultra-minimal config for fast integration tests."""
        return {
            "experiment": {
                "name": "integration_test",
                "seed": 42,
                "save_dir": str(tmp_path / "checkpoints"),
                "log_dir": str(tmp_path / "logs"),
                "device": "cpu"
            },
            "environment": {
                "name": "moving_goals",
                "num_agents": 2,
                "max_episode_steps": 5
            },
            "agent": {
                "hierarchical": True,
                "num_skills": 2,
                "skill_length": 2,
                "attention": {
                    "enabled": True,
                    "embed_dim": 8,
                    "num_heads": 1,
                    "dropout": 0.0
                }
            },
            "training": {
                "algorithm": "ppo",
                "total_timesteps": 20,
                "learning_rate": 1e-2,
                "batch_size": 2,
                "num_epochs": 1,
                "eval_freq": 10,
                "eval_episodes": 1
            },
            "logging": {
                "log_interval": 5,
                "tensorboard": False,
                "wandb": False,
                "metrics": ["episode_reward"]
            },
            "checkpoint": {
                "save_best": False,
                "save_last": False,
                "save_interval": 100
            }
        }
    
    def test_complete_training_workflow(self, very_minimal_config):
        """Test complete training workflow from start to finish."""
        set_global_seed(42)
        
        # Create components
        env = load_environment(very_minimal_config["environment"])
        agent = HierarchicalAgent(
            config=very_minimal_config["agent"],
            observation_space=env.observation_space,
            action_space=env.action_space
        )
        trainer = HierarchicalTrainer(
            agent=agent,
            env=env,
            config=very_minimal_config,
            device=torch.device('cpu')
        )
        
        # Mock the RL algorithm update to avoid complex training
        with patch.object(trainer.rl_algorithm, 'update') as mock_update:
            mock_update.return_value = {
                "policy_loss": 0.5,
                "value_loss": 0.3,
                "entropy_loss": 0.1,
                "total_loss": 0.9
            }
            
            # Run training (should complete without errors)
            try:
                trainer.train()
                training_completed = True
            except Exception as e:
                training_completed = False
                print(f"Training failed: {e}")
        
        assert training_completed
        
        # Check that training progressed
        assert trainer.step_count > 0
        assert trainer.episode_count > 0
        
        # Cleanup
        trainer.cleanup()
    
    def test_agent_environment_compatibility(self, very_minimal_config):
        """Test that agent and environment are compatible."""
        set_global_seed(42)
        
        env = load_environment(very_minimal_config["environment"])
        agent = HierarchicalAgent(
            config=very_minimal_config["agent"],
            observation_space=env.observation_space,
            action_space=env.action_space
        )
        
        # Test environment reset
        obs_list = env.reset()
        assert len(obs_list) == very_minimal_config["environment"]["num_agents"]
        
        # Test agent action generation
        for obs in obs_list:
            action, info = agent.act(obs, other_observations=obs_list)
            
            # Check action is valid for environment
            assert env.action_space.contains(action)
            assert isinstance(info, dict)
        
        # Test environment step
        actions = []
        for obs in obs_list:
            action, _ = agent.act(obs, other_observations=obs_list)
            actions.append(action)
        
        next_obs_list, rewards, dones, env_info = env.step(actions)
        
        assert len(next_obs_list) == len(obs_list)
        assert len(rewards) == len(obs_list)
        assert len(dones) == len(obs_list)
        assert isinstance(env_info, dict)
    
    def test_multi_agent_coordination(self, very_minimal_config):
        """Test multi-agent coordination through communication."""
        set_global_seed(42)
        
        env = load_environment(very_minimal_config["environment"])
        
        # Create multiple agents
        agents = []
        for i in range(very_minimal_config["environment"]["num_agents"]):
            agent = HierarchicalAgent(
                config=very_minimal_config["agent"],
                observation_space=env.observation_space,
                action_space=env.action_space,
                agent_id=i
            )
            agents.append(agent)
        
        # Test coordinated action selection
        obs_list = env.reset()
        
        actions = []
        infos = []
        
        for i, agent in enumerate(agents):
            # Each agent sees all observations for communication
            action, info = agent.act(
                observation=obs_list[i],
                other_observations=[obs_list[j] for j in range(len(obs_list)) if j != i],
                deterministic=False
            )
            actions.append(action)
            infos.append(info)
        
        # Check that communication occurred
        for info in infos:
            if "num_communicating_agents" in info:
                assert info["num_communicating_agents"] == len(agents) - 1
    
    def test_training_reproducibility(self, very_minimal_config):
        """Test that training is reproducible with same seed."""
        # Run training twice with same seed
        results = []
        
        for run in range(2):
            set_global_seed(42)  # Same seed
            
            env = load_environment(very_minimal_config["environment"])
            agent = HierarchicalAgent(
                config=very_minimal_config["agent"],
                observation_space=env.observation_space,
                action_space=env.action_space
            )
            
            # Collect a few episodes
            episode_rewards = []
            for _ in range(3):
                obs_list = env.reset()
                episode_reward = 0
                
                for step in range(5):  # Short episodes
                    actions = []
                    for i, obs in enumerate(obs_list):
                        other_obs = [obs_list[j] for j in range(len(obs_list)) if j != i]
                        action, _ = agent.act(obs, other_observations=other_obs, deterministic=True)
                        actions.append(action)
                    
                    obs_list, rewards, dones, _ = env.step(actions)
                    episode_reward += sum(rewards)
                    
                    if any(dones):
                        break
                
                episode_rewards.append(episode_reward)
            
            results.append(episode_rewards)
        
        # Results should be identical (or very close due to floating point)
        for i in range(len(results[0])):
            assert abs(results[0][i] - results[1][i]) < 1e-6


if __name__ == "__main__":
    pytest.main([__file__])
