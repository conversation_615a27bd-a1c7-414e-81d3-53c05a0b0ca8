"""
Unit tests for reinforcement learning algorithms.

Tests PPO, A2C, and replay buffer implementations.
"""

import pytest
import torch
import torch.nn as nn
import numpy as np
from unittest.mock import Mock, patch, MagicMock

from src.algorithms.ppo import PPOAlgorithm


class TestPPOAlgorithm:
    """Test cases for PPO algorithm."""
    
    @pytest.fixture
    def mock_agent(self):
        """Create mock agent for testing."""
        agent = Mock()
        agent.hierarchical = True
        agent.device = torch.device('cpu')
        
        # Mock policy components
        agent.policy = Mock()
        agent.policy.high_level_policy = Mock()
        agent.policy.low_level_policy = Mock()
        agent.policy.high_level_policy.parameters.return_value = [torch.randn(10, 5, requires_grad=True)]
        agent.policy.low_level_policy.parameters.return_value = [torch.randn(8, 4, requires_grad=True)]
        
        # Mock value networks
        agent.value_network = Mock()
        agent.value_network.parameters.return_value = [torch.randn(5, 3, requires_grad=True)]
        agent.high_value_network = Mock()
        agent.high_value_network.parameters.return_value = [torch.randn(6, 2, requires_grad=True)]
        
        return agent
    
    @pytest.fixture
    def ppo_config(self):
        """PPO configuration for testing."""
        return {
            "learning_rate": 3e-4,
            "clip_range": 0.2,
            "entropy_coef": 0.01,
            "value_loss_coef": 0.5,
            "max_grad_norm": 0.5,
            "num_epochs": 4,
            "batch_size": 64,
            "high_level_lr": 1e-4,
            "low_level_lr": 5e-4
        }
    
    @pytest.fixture
    def ppo_algorithm(self, mock_agent, ppo_config):
        """Create PPO algorithm instance."""
        return PPOAlgorithm(mock_agent, ppo_config, torch.device('cpu'))
    
    def test_ppo_initialization(self, ppo_algorithm, ppo_config):
        """Test PPO algorithm initialization."""
        assert ppo_algorithm.learning_rate == ppo_config["learning_rate"]
        assert ppo_algorithm.clip_range == ppo_config["clip_range"]
        assert ppo_algorithm.entropy_coef == ppo_config["entropy_coef"]
        assert ppo_algorithm.value_loss_coef == ppo_config["value_loss_coef"]
        assert ppo_algorithm.max_grad_norm == ppo_config["max_grad_norm"]
        assert ppo_algorithm.num_epochs == ppo_config["num_epochs"]
        assert ppo_algorithm.batch_size == ppo_config["batch_size"]
    
    def test_ppo_hierarchical_optimizers(self, ppo_algorithm):
        """Test that hierarchical optimizers are created."""
        assert hasattr(ppo_algorithm, 'high_optimizer')
        assert hasattr(ppo_algorithm, 'low_optimizer')
        assert hasattr(ppo_algorithm, 'value_optimizer')
        assert hasattr(ppo_algorithm, 'high_value_optimizer')
    
    def test_ppo_flat_optimizer(self, ppo_config):
        """Test PPO with flat (non-hierarchical) agent."""
        flat_agent = Mock()
        flat_agent.hierarchical = False
        flat_agent.device = torch.device('cpu')
        flat_agent.parameters.return_value = [torch.randn(10, 5, requires_grad=True)]
        
        ppo = PPOAlgorithm(flat_agent, ppo_config, torch.device('cpu'))
        
        assert hasattr(ppo, 'optimizer')
        assert not hasattr(ppo, 'high_optimizer')
    
    def test_compute_advantages(self, ppo_algorithm):
        """Test advantage computation using GAE."""
        batch_size = 10
        obs_dim = 8
        
        observations = torch.randn(batch_size, obs_dim)
        rewards = torch.randn(batch_size)
        dones = torch.zeros(batch_size)  # No episode terminations
        
        # Mock value network output
        values = torch.randn(batch_size)
        ppo_algorithm.agent.value_network.return_value = values.unsqueeze(-1)
        
        advantages, returns = ppo_algorithm._compute_advantages(observations, rewards, dones)
        
        assert advantages.shape == rewards.shape
        assert returns.shape == rewards.shape
        
        # Check that advantages are normalized (mean ≈ 0, std ≈ 1)
        assert abs(advantages.mean().item()) < 0.1
        assert abs(advantages.std().item() - 1.0) < 0.1
    
    def test_evaluate_actions(self, ppo_algorithm):
        """Test action evaluation for log probabilities and values."""
        batch_size = 8
        obs_dim = 6
        action_dim = 2
        
        observations = torch.randn(batch_size, obs_dim)
        actions = torch.randn(batch_size, action_dim)
        
        # Mock value network
        values = torch.randn(batch_size)
        ppo_algorithm.agent.value_network.return_value = values.unsqueeze(-1)
        
        log_probs, values_out = ppo_algorithm._evaluate_actions(observations, actions)
        
        assert log_probs.shape == (batch_size,)
        assert values_out.shape == (batch_size,)
    
    def test_compute_losses(self, ppo_algorithm):
        """Test PPO loss computation."""
        batch_size = 16
        
        observations = torch.randn(batch_size, 8)
        actions = torch.randn(batch_size, 2)
        advantages = torch.randn(batch_size)
        returns = torch.randn(batch_size)
        old_log_probs = torch.randn(batch_size)
        old_values = torch.randn(batch_size)
        
        # Mock evaluate_actions
        current_log_probs = torch.randn(batch_size)
        current_values = torch.randn(batch_size)
        
        with patch.object(ppo_algorithm, '_evaluate_actions', 
                         return_value=(current_log_probs, current_values)):
            
            policy_loss, value_loss, entropy_loss = ppo_algorithm._compute_losses(
                observations, actions, advantages, returns, old_log_probs, old_values
            )
        
        assert isinstance(policy_loss, torch.Tensor)
        assert isinstance(value_loss, torch.Tensor)
        assert isinstance(entropy_loss, torch.Tensor)
        
        assert policy_loss.dim() == 0  # Scalar
        assert value_loss.dim() == 0   # Scalar
        assert entropy_loss.dim() == 0 # Scalar
    
    def test_update_networks_hierarchical(self, ppo_algorithm):
        """Test network updates for hierarchical agent."""
        policy_loss = torch.tensor(0.5, requires_grad=True)
        value_loss = torch.tensor(0.3, requires_grad=True)
        entropy_loss = torch.tensor(0.1, requires_grad=True)
        
        # Mock optimizers
        ppo_algorithm.high_optimizer = Mock()
        ppo_algorithm.low_optimizer = Mock()
        ppo_algorithm.value_optimizer = Mock()
        
        # Mock gradient clipping
        with patch('torch.nn.utils.clip_grad_norm_') as mock_clip:
            ppo_algorithm._update_networks(policy_loss, value_loss, entropy_loss)
        
        # Check that optimizers were called
        ppo_algorithm.high_optimizer.zero_grad.assert_called()
        ppo_algorithm.low_optimizer.zero_grad.assert_called()
        ppo_algorithm.value_optimizer.zero_grad.assert_called()
        
        ppo_algorithm.high_optimizer.step.assert_called()
        ppo_algorithm.low_optimizer.step.assert_called()
        ppo_algorithm.value_optimizer.step.assert_called()
        
        # Check gradient clipping was called
        assert mock_clip.call_count >= 3
    
    def test_update_networks_flat(self, ppo_config):
        """Test network updates for flat agent."""
        flat_agent = Mock()
        flat_agent.hierarchical = False
        flat_agent.device = torch.device('cpu')
        flat_agent.parameters.return_value = [torch.randn(5, 3, requires_grad=True)]
        
        ppo = PPOAlgorithm(flat_agent, ppo_config, torch.device('cpu'))
        ppo.optimizer = Mock()
        
        policy_loss = torch.tensor(0.5, requires_grad=True)
        value_loss = torch.tensor(0.3, requires_grad=True)
        entropy_loss = torch.tensor(0.1, requires_grad=True)
        
        with patch('torch.nn.utils.clip_grad_norm_'):
            ppo._update_networks(policy_loss, value_loss, entropy_loss)
        
        ppo.optimizer.zero_grad.assert_called_once()
        ppo.optimizer.step.assert_called_once()
    
    def test_full_update_cycle(self, ppo_algorithm):
        """Test full PPO update cycle."""
        batch_size = 32
        obs_dim = 8
        action_dim = 2
        
        # Create training data
        training_data = {
            "observations": torch.randn(batch_size, obs_dim),
            "actions": torch.randn(batch_size, action_dim),
            "rewards": torch.randn(batch_size),
            "dones": torch.zeros(batch_size)
        }
        
        # Mock all necessary methods
        advantages = torch.randn(batch_size)
        returns = torch.randn(batch_size)
        
        with patch.object(ppo_algorithm, '_compute_advantages', 
                         return_value=(advantages, returns)), \
             patch.object(ppo_algorithm, '_evaluate_actions', 
                         return_value=(torch.randn(batch_size), torch.randn(batch_size))), \
             patch.object(ppo_algorithm, '_compute_losses', 
                         return_value=(torch.tensor(0.5), torch.tensor(0.3), torch.tensor(0.1))), \
             patch.object(ppo_algorithm, '_update_networks'):
            
            metrics = ppo_algorithm.update(training_data)
        
        assert isinstance(metrics, dict)
        assert "policy_loss" in metrics
        assert "value_loss" in metrics
        assert "entropy_loss" in metrics
        assert "total_loss" in metrics
    
    def test_training_stats_tracking(self, ppo_algorithm):
        """Test that training statistics are properly tracked."""
        # Initially empty
        assert len(ppo_algorithm.training_stats["policy_loss"]) == 0
        
        # Mock a simple update
        batch_size = 16
        training_data = {
            "observations": torch.randn(batch_size, 8),
            "actions": torch.randn(batch_size, 2),
            "rewards": torch.randn(batch_size),
            "dones": torch.zeros(batch_size)
        }
        
        with patch.object(ppo_algorithm, '_compute_advantages', 
                         return_value=(torch.randn(batch_size), torch.randn(batch_size))), \
             patch.object(ppo_algorithm, '_evaluate_actions', 
                         return_value=(torch.randn(batch_size), torch.randn(batch_size))), \
             patch.object(ppo_algorithm, '_compute_losses', 
                         return_value=(torch.tensor(0.5), torch.tensor(0.3), torch.tensor(0.1))), \
             patch.object(ppo_algorithm, '_update_networks'):
            
            ppo_algorithm.update(training_data)
        
        # Check that stats were updated
        assert len(ppo_algorithm.training_stats["policy_loss"]) > 0
        assert len(ppo_algorithm.training_stats["value_loss"]) > 0
        assert len(ppo_algorithm.training_stats["entropy_loss"]) > 0
        assert len(ppo_algorithm.training_stats["total_loss"]) > 0
    
    def test_get_training_stats(self, ppo_algorithm):
        """Test getting training statistics."""
        # Add some dummy stats
        ppo_algorithm.training_stats["policy_loss"] = [0.5, 0.4, 0.3]
        ppo_algorithm.training_stats["value_loss"] = [0.3, 0.2, 0.1]
        
        stats = ppo_algorithm.get_training_stats()
        
        assert isinstance(stats, dict)
        assert "policy_loss_history" in stats
        assert "value_loss_history" in stats
        assert len(stats["policy_loss_history"]) == 3
        assert len(stats["value_loss_history"]) == 3
    
    def test_ppo_with_different_batch_sizes(self, ppo_algorithm):
        """Test PPO with different batch sizes."""
        # Test with batch size smaller than configured batch size
        small_batch_size = 8  # Smaller than default 64
        
        training_data = {
            "observations": torch.randn(small_batch_size, 8),
            "actions": torch.randn(small_batch_size, 2),
            "rewards": torch.randn(small_batch_size),
            "dones": torch.zeros(small_batch_size)
        }
        
        with patch.object(ppo_algorithm, '_compute_advantages', 
                         return_value=(torch.randn(small_batch_size), torch.randn(small_batch_size))), \
             patch.object(ppo_algorithm, '_evaluate_actions', 
                         return_value=(torch.randn(small_batch_size), torch.randn(small_batch_size))), \
             patch.object(ppo_algorithm, '_compute_losses', 
                         return_value=(torch.tensor(0.5), torch.tensor(0.3), torch.tensor(0.1))), \
             patch.object(ppo_algorithm, '_update_networks'):
            
            metrics = ppo_algorithm.update(training_data)
        
        assert isinstance(metrics, dict)
    
    def test_ppo_gradient_clipping(self, ppo_algorithm):
        """Test that gradient clipping is applied."""
        policy_loss = torch.tensor(0.5, requires_grad=True)
        value_loss = torch.tensor(0.3, requires_grad=True)
        entropy_loss = torch.tensor(0.1, requires_grad=True)
        
        with patch('torch.nn.utils.clip_grad_norm_') as mock_clip:
            ppo_algorithm._update_networks(policy_loss, value_loss, entropy_loss)
        
        # Should be called for each parameter group
        assert mock_clip.call_count > 0
        
        # Check that max_grad_norm is used
        for call in mock_clip.call_args_list:
            args, kwargs = call
            assert len(args) >= 2
            assert args[1] == ppo_algorithm.max_grad_norm


class TestAlgorithmIntegration:
    """Integration tests for algorithm components."""
    
    def test_ppo_with_real_tensors(self):
        """Test PPO with realistic tensor operations."""
        # Create a simple mock agent with actual parameters
        class SimpleAgent:
            def __init__(self):
                self.hierarchical = False
                self.device = torch.device('cpu')
                self.linear = nn.Linear(4, 2)
                self.value_net = nn.Linear(4, 1)
            
            def parameters(self):
                return list(self.linear.parameters()) + list(self.value_net.parameters())
        
        agent = SimpleAgent()
        config = {
            "learning_rate": 1e-3,
            "clip_range": 0.2,
            "num_epochs": 2,
            "batch_size": 8
        }
        
        ppo = PPOAlgorithm(agent, config, torch.device('cpu'))
        
        # Create realistic training data
        batch_size = 16
        training_data = {
            "observations": torch.randn(batch_size, 4),
            "actions": torch.randn(batch_size, 2),
            "rewards": torch.randn(batch_size),
            "dones": torch.zeros(batch_size)
        }
        
        # Mock the agent-specific methods that PPO calls
        with patch.object(ppo, '_evaluate_actions') as mock_eval:
            mock_eval.return_value = (torch.randn(batch_size), torch.randn(batch_size))
            
            # This should not raise any errors
            metrics = ppo.update(training_data)
            
            assert isinstance(metrics, dict)
            assert all(isinstance(v, float) for v in metrics.values())


if __name__ == "__main__":
    pytest.main([__file__])
